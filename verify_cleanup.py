#!/usr/bin/env python3
"""
Final cleanup verification - Test core pipeline functionality
"""
import os
import sys

def main():
    print("[VERIFY] Final Pipeline Cleanup Verification")
    print("=" * 50)
    
    try:
        # Test core imports
        print("\n1. Testing core imports...")
        from preprocessing.ml_core import MODEL_REGISTRY, impute_logs, impute_logs_deep
        from config_handler import configure_hyperparameters, configure_gpu_optimization
        from preprocessing.data_handler import clean_log_data, load_las_files_from_directory
        from reporting import generate_qc_report, create_summary_plots
        print("   [SUCCESS] All critical imports successful")
        
        # Test MODEL_REGISTRY
        print("\n2. Testing MODEL_REGISTRY...")
        models = list(MODEL_REGISTRY.keys())
        print(f"   [SUCCESS] Available models ({len(models)}): {models}")
        
        # Verify model configurations
        print("\n3. Verifying model configurations...")
        all_valid = True
        for model_key, config in MODEL_REGISTRY.items():
            required_keys = ['type', 'model_class', 'hyperparameters', 'name']
            missing = [key for key in required_keys if key not in config]
            if missing:
                print(f"   [ERROR] {model_key}: Missing {missing}")
                all_valid = False
            else:
                print(f"   [SUCCESS] {model_key}: Complete configuration")
        
        # Test hyperparameter configuration
        print("\n4. Testing hyperparameter configuration...")
        hparams = configure_hyperparameters()
        print(f"   [SUCCESS] Hyperparameters configured for {len(hparams)} models")
        
        # Test GPU configuration
        print("\n5. Testing GPU configuration...")
        gpu_config = configure_gpu_optimization()
        print(f"   [SUCCESS] GPU config: {gpu_config.get('optimization_strategy', 'Not specified')}")
        
        # Summary
        print("\n" + "=" * 50)
        print("[SUMMARY] CLEANUP VERIFICATION SUMMARY")
        print("=" * 50)
        
        if all_valid:
            print("[SUCCESS] All model configurations valid")
        else:
            print("[ERROR] Some model configurations incomplete")
            
        print(f"[SUCCESS] {len(models)} models available in registry")
        print(f"[SUCCESS] {len(hparams)} models have hyperparameters")
        print("[SUCCESS] All critical functions accessible")
        print("[SUCCESS] No import errors detected")
        
        print(f"\n[STATUS] Overall Status: {'PIPELINE READY' if all_valid else 'MINOR ISSUES'}")
        
        return all_valid
        
    except Exception as e:
        print(f"\n[ERROR] Critical Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
