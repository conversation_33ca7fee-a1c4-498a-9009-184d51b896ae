# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive machine learning pipeline for predicting and imputing missing well log data using advanced algorithms including gradient boosting models, a basic neural network, and traditional statistical methods. The project features GPU acceleration, advanced preprocessing, and comprehensive visualization capabilities.

## Environment Setup

### Virtual Environment
The project uses a virtual environment called `mwlt`. Create and activate it before running any commands:
```bash
# Create environment
python -m venv mwlt

# Activate environment
# Windows:
mwlt\Scripts\activate
# Linux/Mac:
source mwlt/bin/activate
```

### Dependencies
Install required packages from requirements.txt:
```bash
pip install -r requirements.txt
```

### GPU Support
The project supports GPU acceleration with CUDA. Key GPU-accelerated components:
- XGBoost with `device='cuda'`
- PyTorch models for deep learning
- CatBoost with `task_type='GPU'`

## Common Commands

### Running the Main Pipeline
```bash
python main.py
```
This launches the interactive GUI workflow for the complete ML pipeline.

### Code Quality
```bash
# Linting (flake8 is available)
python -m flake8 main.py preprocessing/ml_core.py preprocessing/data_handler.py

# No pytest framework is currently installed
```

### Testing Individual Components
Active test files in the root directory:

```bash
# Test current pipeline functionality
python test_pipeline.py

# Test new model implementations
python test_new_models.py

# Verify post-cleanup functionality
python verify_cleanup.py
```

Archived test files for comprehensive testing:
```bash
# Test memory optimization (comprehensive test for large datasets)
python archives/test_memory_optimization.py

# Test integration functionality
python archives/test_integration.py

# GPU-specific tests
python archives/gpu_process/test_gpu.py
python archives/gpu_process/test_advanced_models_gpu.py
```

## Architecture Overview

### Core Pipeline Files
- **`main.py`**: Entry point with interactive GUI workflow orchestrating the complete ML pipeline
- **`preprocessing/data_handler.py`**: LAS file operations, data loading, cleaning, and preprocessing
- **`preprocessing/ml_core.py`**: Machine learning model registry and training pipeline with MODEL_REGISTRY
- **`config_handler.py`**: User interfaces, file selection dialogs, and configuration management
- **`reporting.py`**: Visualization, analysis, and performance reporting

### Model Implementation Structure
- **`models/`**: Contains all ML model implementations
  - `__init__.py`: Package initialization with model registry
  - `neuralnet.py`: Basic neural network implementation

### Utility Modules
- **`utils/`**: Specialized functionality (17+ utility modules)
  - `gpu_acceleration.py`: GPU optimization and CUDA operations  
  - `xgboost_gpu_utils.py`: XGBoost-specific GPU configurations
  - `display_utils.py`: Cross-platform display formatting
  - `memory_optimization.py`: Memory management utilities
  - `performance_monitor.py`: Performance tracking and benchmarking
  - `data_leakage_detector.py`: Data validation utilities
  - `mixed_precision_utils.py`: Mixed precision training optimization
  - `gpu_fallback.py`: GPU to CPU fallback mechanisms
  - `hyperparameter_tuning.py`: Automated hyperparameter optimization
  - `windows_compatibility.py`: Windows-specific compatibility fixes

### Data and Configuration
- **`Las/`**: Input LAS (Log ASCII Standard) well log files
- **`config/display_config.ini`**: Display and visualization settings
- **`plots/`**: Generated visualization outputs
- **`requirements.txt`**: Python package dependencies

## Model Categories

### Gradient Boosting Models (GPU-Accelerated)
- **XGBoost**: Modern GPU acceleration with `device='cuda'`
- **LightGBM**: High-performance gradient boosting with `device='gpu'`
- **CatBoost**: Categorical feature handling with `task_type='GPU'`

### Deep Learning Models
- **Basic Neural Network**: Simple neural network for baseline comparison and feature learning
- **Memory Optimization**: Handles large datasets through adaptive batch processing

### Statistical Models
- **Linear Regression**: Interpretable baseline with diagnostics
- **Ridge Regression**: L2 regularization for multicollinearity

## Workflow Steps

1. **File Selection**: GUI dialog for LAS file selection
2. **Data Loading**: Automated LAS file processing with error handling
3. **Log Configuration**: Feature and target log selection
4. **Training Strategy**: Well separation and prediction mode configuration
5. **Model Selection**: Multi-model selection from MODEL_REGISTRY
6. **Execution**: Batch model training and evaluation
7. **Analysis**: Performance comparison and ranking
8. **Visualization**: Comprehensive plotting and quality control
9. **Output**: Results export to LAS files and reports

## Key Features

### Multi-Model Comparison
- Batch execution of multiple models simultaneously
- Automated performance ranking based on composite scores
- Side-by-side visualization comparisons
- Statistical evaluation with MAE, R², RMSE metrics

### Advanced Data Processing
- Smart data cleaning with domain-specific rules for well log data
- Enhanced preprocessing with outlier detection
- Data leakage detection for model validation
- Sequence creation for deep learning models

### Professional Visualization
- Quality control plots with cross-plot analysis
- Model performance dashboards and residual analysis
- Publication-ready charts with customizable styling
- Multi-model comparison visualizations

## Development Notes

### Model Registry
The `MODEL_REGISTRY` in `preprocessing/ml_core.py` contains all available models. New models should be registered here with proper configuration including:
- **type**: 'shallow' (gradient boosting, statistical), or 'deep' (basic neural networks)
- **model_class**: Reference to the model implementation class
- **requires_sequences**: Boolean indicating if the model needs sequence data
- **config**: Model-specific hyperparameters and settings

Example registry entry:
```python
'xgboost': {
    'type': 'shallow',
    'model_class': XGBRegressor,
    'requires_sequences': False,
    'config': {'device': 'cuda', 'tree_method': 'gpu_hist'}
}
```

### GPU Memory Management
The codebase includes automatic GPU detection with CPU fallback strategies. Memory optimization utilities handle large datasets efficiently.

### File Structure Dependencies
Core pipeline execution requires all files in the dependency chain:
```
main.py
├── config_handler.py (UI, file selection, configuration)
├── preprocessing/
│   ├── data_handler.py (LAS loading, cleaning, preprocessing)
│   ├── ml_core.py (model registry, training pipeline)
│   └── ml_core_phase1_integration.py (enhanced preprocessing)
├── models/ (all model implementations)
│   ├── __init__.py
│   └── neuralnet.py (basic neural network)
├── utils/ (GPU, memory, performance utilities)
│   ├── memory_optimization.py
│   ├── gpu_acceleration.py
│   ├── gpu_fallback.py
│   └── [15+ other utility modules]
└── reporting.py (visualization, analysis, output)
```

Active test files for validation:
- `test_pipeline.py`: Core pipeline functionality verification
- `test_new_models.py`: New model implementation testing
- `verify_cleanup.py`: Post-cleanup functionality verification

### Interactive Workflow
The main pipeline provides extensive user interaction through console selections and GUI dialogs for file management, making it suitable for both technical users and domain experts.

### Memory Optimization (Phase 1 - Completed ✅)
The project includes comprehensive memory optimization features:
- **Environment Configuration**: `PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True` set before PyTorch imports
- **Memory Monitoring**: Real-time memory usage tracking via `utils.memory_optimization`
- **Mixed Precision Training**: Automatic AMP (Automatic Mixed Precision) for deep learning models
- **GPU Fallback**: Graceful fallback to CPU when GPU memory is insufficient
- **Adaptive Batch Processing**: Memory-efficient batch processing for large datasets (27,618+ samples)
- **Emergency OOM Recovery**: Automatic out-of-memory recovery with batch size reduction
- **Memory-Efficient Prediction**: Enhanced deep learning prediction with progressive batch processing
- **Memory Testing**: Comprehensive test suite (`test_memory_optimization.py`) for validation

### GPU Memory Management for Large Datasets
Special optimizations for handling large datasets like 27,618 samples:
- **Optimal Batch Size Calculation**: Automatically calculates best batch size based on available GPU memory
- **Progressive Batch Processing**: Processes data in manageable chunks to prevent OOM errors
- **Real-time Memory Monitoring**: Tracks GPU memory usage during processing with progress updates
- **Emergency Recovery**: Automatic batch size reduction and CPU fallback when memory issues occur
- **Memory Cleanup**: Aggressive CUDA cache clearing between batches

### Phase Implementation Status
- **Phase 1**: Memory optimization and basic model stability ✅
- **Phase 2**: Gradient boosting models and basic neural network ✅
- **Phase 3**: Enhanced preprocessing and validation - In Progress
- **Phase 4**: Performance optimization and deployment - Planned

### Recent Updates (2025-01-27)
- **Repository Cleanup Completed**: Comprehensive codebase organization with clean root directory
- **Test Files Archived**: All test files moved to organized archive structure for clean development environment
- **Generated Content Removed**: Cleaned `__pycache__/`, `catboost_info/`, `tutorial_results/`, and old plot files
- **Memory Optimization**: Full implementation with adaptive batch processing for large datasets (27,618+ samples)
- **GPU Memory Management**: Advanced CUDA memory handling with OOM recovery and CPU fallback
- **Clean Development Environment**: Root directory contains only active pipeline files and essential utilities
- **Organized Archives**: Historical materials preserved in logical, accessible locations
- **Updated Documentation**: All documentation reflects current clean codebase structure
- **Documentation Cleanup**: Removed specific Phase 3 documentation files from cleanup tracking as requested
- **Model Simplification**: Removed SAITS, BRITS, Transformer, and mRNN models to focus on gradient boosting and basic neural networks

## File Organization and Cleanup Status

### **Clean Root Directory (Active Development Only)**
- **Core Pipeline**: `main.py`, `data_handler.py`, `ml_core.py`, `config_handler.py`, `reporting.py`
- **Advanced Processing**: `enhanced_preprocessing.py`, `ml_core_phase1_integration.py`
- **Utilities**: `gradient_diagnostics.py`, `data_leakage_detector.py`, `mlr_utils.py`
- **Dependencies**: `requirements.txt`
- **Essential Directories**: `models/`, `utils/`, `Las/`, `config/`, `plots/`

### **Organized Archive Structure**
- **Test Files**: All moved to `archives/` directory for organized testing
  - `archives/test_memory_optimization.py`, `archives/test_integration.py`
  - `archives/gpu_process/` - GPU-specific tests and utilities
  - `archives/test_files/` - Comprehensive test suite
- **Historical Development**: `archives/second_stage/`, `archives/fix/`
- **Documentation**: `docs/` directory with legacy documentation
- **Examples**: `example/` directory with PyPOTS tutorials

### **Cleaned Generated Content**
- **Removed**: `__pycache__/`, `catboost_info/`, `tutorial_results/`, old plot files
- **Auto-regenerated**: Content will be recreated during normal pipeline execution

### **Excluded from Cleanup Tracking**
- `__pycache__/` directories (auto-generated bytecode)
- `README.md` (main project documentation)
- `@CLAUDE.md` (this file - active context documentation)
- `@codebase_structure.md` (updated main structure documentation)
- `@List_of_cleaned_file.md` (cleanup tracking documentation)

## Repository Structure Overview

After comprehensive cleanup, the repository maintains optimal organization:

### **Current Clean Structure**
```
branch_3_gpu_base/
├── 📄 CORE PIPELINE (Root Directory)
│   ├── main.py                    # Main entry point with GUI workflow
│   ├── config_handler.py         # User interfaces and configuration
│   ├── reporting.py              # Visualization and analysis
│   ├── requirements.txt          # Dependencies
│   ├── test_pipeline.py          # Pipeline functionality verification
│   ├── test_new_models.py        # New model testing
│   └── verify_cleanup.py         # Cleanup verification
│
├── 📁 PREPROCESSING DIRECTORY
│   ├── preprocessing/
│   │   ├── __init__.py
│   │   ├── data_handler.py       # LAS file operations and preprocessing
│   │   ├── ml_core.py            # Model registry and training pipeline
│   │   └── ml_core_phase1_integration.py  # Enhanced preprocessing
│
├── 📁 ESSENTIAL DIRECTORIES
│   ├── models/                   # All model implementations
│   │   ├── __init__.py
│   │   └── neuralnet.py         # Basic neural network
│   ├── utils/                    # 17+ utility modules and optimization
│   ├── Las/                      # Input LAS files
│   ├── config/                   # Configuration files
│   └── plots/                    # Generated visualization outputs
│
├── 📁 ORGANIZED ARCHIVES
│   ├── archives/                 # All test files and historical code
│   │   ├── test_memory_optimization.py
│   │   ├── test_integration.py
│   │   ├── gpu_process/         # GPU-specific tests
│   │   └── test_files/          # Comprehensive test suite
│   ├── docs/                     # Legacy documentation
│   └── example/                  # PyPOTS examples and tutorials
│
└── 📄 ACTIVE DOCUMENTATION
    ├── README.md                 # Main project documentation
    ├── CLAUDE.md                 # This context documentation
    └── List_of_cleaned_file.md   # Cleanup tracking documentation
```

This organization provides:
1. **Clean Development Environment**: Root directory contains only active pipeline files
2. **Organized Testing**: All tests preserved in accessible archive structure  
3. **Zero Functional Impact**: All core functionality preserved and enhanced
4. **Easy Maintenance**: Clear separation between active code and historical materials