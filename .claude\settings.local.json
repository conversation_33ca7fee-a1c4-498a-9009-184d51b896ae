{"permissions": {"allow": ["mcp__sequentialthinking__sequentialthinking", "mcp__brave__brave_web_search", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "Bash(del test_phase1_deprecation.py simple_phase1_test.py)", "Bash(move test_pipeline.py archives )", "Bash(move test_new_models.py archives )", "Bash(move verify_cleanup.py archives)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(del debug_models.py)", "<PERSON><PERSON>(dir:*)", "<PERSON><PERSON>(findstr:*)", "Bash(if not exist \"archives\" mkdir archives)", "Bash(del \"C:\\Users\\<USER>\\OneDrive - PT Pertamina (Persero)\\13_Python_PKB\\2_Pyth_Script\\18_MWLT_Log\\branch_3_gpu_base\\test_matplotlib_fix.py\")"], "deny": [], "ask": []}}