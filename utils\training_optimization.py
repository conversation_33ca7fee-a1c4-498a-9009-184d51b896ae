"""
Training Optimization Utilities for mRNN and Deep Learning Models

This module provides advanced training optimizations including:
- Dynamic batch size optimization
- Gradient accumulation support
- Memory usage optimization
- Performance monitoring and adaptive adjustments
"""

import torch
import torch.nn as nn
from typing import Dict, Any, Optional, Tuple, List
import psutil
import time
import warnings


class TrainingOptimizer:
    """Advanced training optimizer for deep learning models."""
    
    def __init__(self, device: torch.device = None):
        """Initialize training optimizer."""
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.gpu_available = torch.cuda.is_available()
        self.optimization_history = []
        
    def get_optimal_batch_size(self, model: nn.Module, input_shape: Tuple[int, ...], 
                              max_memory_usage: float = 0.8, start_batch_size: int = 32) -> int:
        """
        Dynamically find optimal batch size based on available GPU memory.
        
        Args:
            model: PyTorch model
            input_shape: Input tensor shape (sequence_len, n_features)
            max_memory_usage: Maximum GPU memory usage (0.0-1.0)
            start_batch_size: Starting batch size for testing
            
        Returns:
            Optimal batch size
        """
        if not self.gpu_available:
            return min(start_batch_size, 64)  # Conservative for CPU
        
        # Get available GPU memory
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory
        gpu_memory_free = gpu_memory_total - torch.cuda.memory_allocated()
        target_memory = gpu_memory_free * max_memory_usage
        
        print(f"🔍 Finding optimal batch size...")
        print(f"   GPU memory: {gpu_memory_total / 1e9:.1f} GB total, {gpu_memory_free / 1e9:.1f} GB free")
        
        # Binary search for optimal batch size
        min_batch = 1
        max_batch = start_batch_size * 4  # Start with 4x the initial size
        optimal_batch = start_batch_size
        
        model.eval()  # Set to eval mode for testing
        
        for batch_size in [start_batch_size, start_batch_size * 2, start_batch_size * 3]:
            try:
                # Create test batch
                test_input = torch.randn(batch_size, *input_shape, device=self.device)
                test_mask = torch.ones_like(test_input, dtype=torch.bool, device=self.device)
                
                # Clear cache before test
                torch.cuda.empty_cache()
                memory_before = torch.cuda.memory_allocated()
                
                # Forward pass test
                with torch.no_grad():
                    _ = model(test_input, test_mask)
                
                memory_after = torch.cuda.memory_allocated()
                memory_used = memory_after - memory_before
                
                if memory_used < target_memory:
                    optimal_batch = batch_size
                    print(f"   ✓ Batch size {batch_size}: {memory_used / 1e6:.1f} MB")
                else:
                    print(f"   ✗ Batch size {batch_size}: {memory_used / 1e6:.1f} MB (too large)")
                    break
                    
                # Clean up
                del test_input, test_mask
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"   ✗ Batch size {batch_size}: OOM")
                    break
                else:
                    raise e
        
        print(f"🎯 Optimal batch size: {optimal_batch}")
        return optimal_batch
    
    def setup_gradient_accumulation(self, effective_batch_size: int, 
                                   actual_batch_size: int) -> Tuple[int, bool]:
        """
        Setup gradient accumulation to achieve effective larger batch sizes.
        
        Args:
            effective_batch_size: Desired effective batch size
            actual_batch_size: Actual batch size that fits in memory
            
        Returns:
            Tuple of (accumulation_steps, use_accumulation)
        """
        if effective_batch_size <= actual_batch_size:
            return 1, False
        
        accumulation_steps = effective_batch_size // actual_batch_size
        use_accumulation = accumulation_steps > 1
        
        if use_accumulation:
            print(f"📊 Gradient accumulation enabled:")
            print(f"   Effective batch size: {effective_batch_size}")
            print(f"   Actual batch size: {actual_batch_size}")
            print(f"   Accumulation steps: {accumulation_steps}")
        
        return accumulation_steps, use_accumulation
    
    def optimize_dataloader_settings(self, dataset_size: int, batch_size: int) -> Dict[str, Any]:
        """
        Optimize DataLoader settings based on system capabilities.
        
        Args:
            dataset_size: Size of the dataset
            batch_size: Batch size
            
        Returns:
            Optimized DataLoader settings
        """
        # Get system info
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / 1e9
        
        # Calculate optimal number of workers
        if dataset_size < 1000:
            num_workers = 0  # Small dataset, no benefit from multiprocessing
        elif dataset_size < 10000:
            num_workers = min(2, cpu_count // 2)
        else:
            num_workers = min(8, cpu_count - 1)  # Leave one CPU core free
        
        # Adjust for Windows compatibility
        import platform
        if platform.system() == 'Windows' and num_workers > 4:
            num_workers = 4  # Windows has issues with too many workers
        
        # Pin memory settings
        pin_memory = self.gpu_available and memory_gb > 8  # Only if enough RAM
        
        # Prefetch factor
        prefetch_factor = 2 if num_workers > 0 else None
        persistent_workers = num_workers > 0
        
        settings = {
            'num_workers': num_workers,
            'pin_memory': pin_memory,
            'prefetch_factor': prefetch_factor,
            'persistent_workers': persistent_workers,
            'drop_last': True if dataset_size > batch_size * 10 else False
        }
        
        print(f"📦 Optimized DataLoader settings:")
        for key, value in settings.items():
            print(f"   {key}: {value}")
        
        return settings
    
    def monitor_training_performance(self, epoch: int, batch_idx: int, 
                                   loss: float, batch_time: float) -> Dict[str, float]:
        """
        Monitor training performance and provide optimization suggestions.
        
        Args:
            epoch: Current epoch
            batch_idx: Current batch index
            loss: Current loss value
            batch_time: Time taken for current batch
            
        Returns:
            Performance metrics
        """
        metrics = {
            'epoch': epoch,
            'batch_idx': batch_idx,
            'loss': loss,
            'batch_time': batch_time,
            'batches_per_second': 1.0 / batch_time if batch_time > 0 else 0,
            'timestamp': time.time()
        }
        
        # Add GPU metrics if available
        if self.gpu_available:
            metrics.update({
                'gpu_memory_used_mb': torch.cuda.memory_allocated() / 1e6,
                'gpu_memory_cached_mb': torch.cuda.memory_reserved() / 1e6,
                'gpu_utilization': self._get_gpu_utilization()
            })
        
        self.optimization_history.append(metrics)
        
        # Provide optimization suggestions every 50 batches
        if batch_idx % 50 == 0 and len(self.optimization_history) > 10:
            self._analyze_performance()
        
        return metrics
    
    def _get_gpu_utilization(self) -> float:
        """Get GPU utilization percentage."""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            return util.gpu
        except:
            return 0.0  # Return 0 if pynvml not available
    
    def _analyze_performance(self):
        """Analyze recent performance and provide suggestions."""
        if len(self.optimization_history) < 10:
            return
        
        recent_metrics = self.optimization_history[-10:]
        avg_batch_time = sum(m['batch_time'] for m in recent_metrics) / len(recent_metrics)
        avg_gpu_util = sum(m.get('gpu_utilization', 0) for m in recent_metrics) / len(recent_metrics)
        
        suggestions = []
        
        if avg_batch_time > 1.0:  # Slow batches
            suggestions.append("⚠️ Slow batch processing detected. Consider reducing batch size or model complexity.")
        
        if avg_gpu_util < 50 and self.gpu_available:
            suggestions.append("⚠️ Low GPU utilization. Consider increasing batch size or using gradient accumulation.")
        
        if avg_gpu_util > 95:
            suggestions.append("⚠️ Very high GPU utilization. Consider reducing batch size to prevent OOM.")
        
        if suggestions:
            print("\n📊 Performance Analysis:")
            for suggestion in suggestions:
                print(f"   {suggestion}")
            print()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get summary of training performance."""
        if not self.optimization_history:
            return {}
        
        metrics = self.optimization_history
        
        return {
            'total_batches': len(metrics),
            'avg_batch_time': sum(m['batch_time'] for m in metrics) / len(metrics),
            'avg_batches_per_second': sum(m['batches_per_second'] for m in metrics) / len(metrics),
            'avg_gpu_memory_used_mb': sum(m.get('gpu_memory_used_mb', 0) for m in metrics) / len(metrics),
            'avg_gpu_utilization': sum(m.get('gpu_utilization', 0) for m in metrics) / len(metrics),
            'training_duration': metrics[-1]['timestamp'] - metrics[0]['timestamp'] if len(metrics) > 1 else 0
        }


def create_training_optimizer(device: torch.device = None) -> TrainingOptimizer:
    """Create and return a training optimizer instance."""
    return TrainingOptimizer(device)


def get_optimal_batch_size_for_model(model_type: str, n_features: int, 
                                   sequence_len: int, device: str) -> int:
    """
    Get optimal batch size based on model type and data dimensions.
    
    Args:
        model_type: Type of model ('autoencoder', 'unet', 'transformer', etc.)
        n_features: Number of features
        sequence_len: Sequence length
        device: Device type ('cuda' or 'cpu')
    
    Returns:
        Optimal batch size
    """
    if device == 'cpu':
        # Conservative for CPU
        base_sizes = {
            'autoencoder': 32,
            'unet': 32,
            'transformer': 16,
            'saits': 16,
            'brits': 64
        }
    else:
        # Optimized for GPU
        base_sizes = {
            'autoencoder': 128,
            'unet': 128,
            'transformer': 192,
            'saits': 64,
            'brits': 256
        }
    
    # Adjust based on sequence length and features
    memory_factor = (64 / sequence_len) * (4 / n_features)
    optimal_size = int(base_sizes.get(model_type, 64) * memory_factor)
    
    # Ensure it's a multiple of 8 for GPU efficiency
    if device == 'cuda':
        optimal_size = (optimal_size // 8) * 8
    
    return max(8, min(optimal_size, 512))
