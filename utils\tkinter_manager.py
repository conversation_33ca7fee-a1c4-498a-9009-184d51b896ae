"""
Tkinter Object Management for Cross-Validation
Provides comprehensive management of tkinter objects to prevent threading errors.
"""

import os
import sys
import gc
import warnings
import threading
import weakref
from typing import Optional, Dict, Any, List
import matplotlib
import matplotlib.pyplot as plt
from contextlib import contextmanager


class TkinterObjectManager:
    """
    Manages tkinter objects and prevents threading-related errors during
    matplotlib backend switching in cross-validation scenarios.
    """
    
    def __init__(self):
        self.original_backend = None
        self.original_interactive = None
        self.original_env = {}
        self.cleanup_callbacks = []
        self._lock = threading.Lock()
        
    def register_cleanup_callback(self, callback):
        """Register a callback to be called during cleanup."""
        with self._lock:
            self.cleanup_callbacks.append(callback)
    
    def force_cleanup_tkinter_objects(self):
        """
        Aggressively clean up tkinter objects that might cause threading issues.
        """
        try:
            # Close all matplotlib figures
            plt.close('all')
            
            # Clear matplotlib's internal state
            if hasattr(plt, '_pylab_helpers'):
                plt._pylab_helpers.Gcf.destroy_all()
            
            # Force garbage collection multiple times
            for _ in range(3):
                gc.collect()
            
            # Try to clear tkinter-related modules from memory
            tkinter_modules = [name for name in sys.modules.keys() 
                             if 'tkinter' in name.lower() or '_tkinter' in name.lower()]
            
            # Don't actually remove modules as it can cause import issues
            # Just clear their caches if possible
            for module_name in tkinter_modules:
                module = sys.modules.get(module_name)
                if module and hasattr(module, '__dict__'):
                    # Clear module-level variables that might hold references
                    for attr_name in list(module.__dict__.keys()):
                        if attr_name.startswith('_') and not attr_name.startswith('__'):
                            try:
                                delattr(module, attr_name)
                            except:
                                pass
            
            # Run any registered cleanup callbacks
            with self._lock:
                for callback in self.cleanup_callbacks:
                    try:
                        callback()
                    except Exception as e:
                        # Silently handle callback errors
                        pass
                        
        except Exception as e:
            # Don't let cleanup errors propagate
            pass
    
    def setup_safe_backend(self, target_backend='Agg'):
        """
        Set up a safe matplotlib backend for cross-validation.
        
        Args:
            target_backend: Backend to switch to (default: 'Agg')
        """
        # Store original state
        self.original_backend = matplotlib.get_backend()
        self.original_interactive = matplotlib.is_interactive()
        self.original_env = {
            'MPLBACKEND': os.environ.get('MPLBACKEND', ''),
            'DISPLAY': os.environ.get('DISPLAY', '')
        }
        
        # Clean up before switching
        self.force_cleanup_tkinter_objects()
        
        # Disable interactive mode
        plt.ioff()
        
        # Set safe backend
        matplotlib.use(target_backend, force=True)
        
        # Update environment
        os.environ['MPLBACKEND'] = target_backend
        
        # Suppress warnings
        self._suppress_all_gui_warnings()
        
        print(f"[BACKEND] Switched to safe backend: {target_backend}")
    
    def restore_original_backend(self):
        """Restore the original matplotlib backend and settings."""
        if self.original_backend is None:
            return
            
        try:
            # Clean up before restoring
            self.force_cleanup_tkinter_objects()
            
            # Restore backend
            matplotlib.use(self.original_backend, force=True)
            
            # Restore interactive mode
            if self.original_interactive:
                plt.ion()
            
            # Restore environment variables
            for key, value in self.original_env.items():
                if value:
                    os.environ[key] = value
                else:
                    os.environ.pop(key, None)
            
            print(f"🔧 Restored original backend: {self.original_backend}")
            
        except Exception as e:
            print(f"⚠️ Warning during backend restoration: {e}")
        
        finally:
            # Reset stored state
            self.original_backend = None
            self.original_interactive = None
            self.original_env = {}
    
    def _suppress_all_gui_warnings(self):
        """Comprehensively suppress GUI-related warnings."""
        warning_patterns = [
            '.*main thread is not in main loop.*',
            '.*Tkinter.*',
            '.*tkinter.*',
            '.*Image.*',
            '.*Variable.*',
            '.*PhotoImage.*',
            '.*GUI.*',
            '.*display.*'
        ]
        
        for pattern in warning_patterns:
            warnings.filterwarnings('ignore', message=pattern)
        
        # Suppress by category
        warnings.filterwarnings('ignore', category=RuntimeWarning, module='matplotlib')
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', category=DeprecationWarning, module='matplotlib')


# Global instance for easy access - with safe initialization
_tkinter_manager = None

def _create_fallback_manager():
    """Create a minimal fallback manager."""
    return type('FallbackManager', (), {
        'setup_safe_backend': lambda self, backend='Agg': None,
        'restore_original_backend': lambda self: None,
        'force_cleanup_tkinter_objects': lambda self: None,
        'register_cleanup_callback': lambda self, callback: None,
        '_suppress_all_gui_warnings': lambda self: None
    })()

def _get_or_create_manager():
    """Get or create the global tkinter manager instance safely."""
    global _tkinter_manager
    if _tkinter_manager is None:
        try:
            _tkinter_manager = TkinterObjectManager()
        except Exception as e:
            print(f"⚠️ Warning: Failed to create tkinter manager: {e}")
            # Create a minimal fallback manager
            _tkinter_manager = _create_fallback_manager()
    return _tkinter_manager


@contextmanager
def safe_cross_validation_context(backend='Agg'):
    """
    Context manager for safe cross-validation execution.

    Usage:
        with safe_cross_validation_context():
            # Perform cross-validation here
            results = cross_validate(...)
    """
    manager = None
    try:
        # Try to get the manager
        try:
            manager = _get_or_create_manager()
        except:
            # If that fails, create a fallback directly
            try:
                manager = _create_fallback_manager()
            except NameError:
                # Ultimate fallback if function not available
                manager = type('UltimateFallbackManager', (), {
                    'setup_safe_backend': lambda self, backend='Agg': None,
                    'restore_original_backend': lambda self: None,
                    'force_cleanup_tkinter_objects': lambda self: None,
                    'register_cleanup_callback': lambda self, callback: None,
                    '_suppress_all_gui_warnings': lambda self: None
                })()

        manager.setup_safe_backend(backend)
        yield manager
    except Exception as e:
        # If everything fails, create a minimal fallback
        print(f"WARNING: Safe cross-validation context failed: {e}")
        try:
            fallback_manager = _create_fallback_manager()
        except NameError:
            # Fallback if even _create_fallback_manager is not available
            fallback_manager = type('UltimateFallbackManager', (), {
                'setup_safe_backend': lambda self, backend='Agg': None,
                'restore_original_backend': lambda self: None,
                'force_cleanup_tkinter_objects': lambda self: None,
                'register_cleanup_callback': lambda self, callback: None,
                '_suppress_all_gui_warnings': lambda self: None
            })()
            print("WARNING: Using ultimate fallback manager")
        yield fallback_manager
    finally:
        # Always try to restore, but don't fail if it doesn't work
        try:
            if manager:
                manager.restore_original_backend()
        except:
            pass


def cleanup_tkinter_objects():
    """Convenience function to clean up tkinter objects."""
    try:
        try:
            manager = _get_or_create_manager()
        except:
            manager = _create_fallback_manager()
        manager.force_cleanup_tkinter_objects()
    except Exception as e:
        print(f"⚠️ Warning: Tkinter cleanup failed: {e}")
        # Continue without cleanup


def register_cleanup_callback(callback):
    """Register a cleanup callback."""
    try:
        try:
            manager = _get_or_create_manager()
        except:
            manager = _create_fallback_manager()
        manager.register_cleanup_callback(callback)
    except Exception as e:
        print(f"⚠️ Warning: Callback registration failed: {e}")
        # Continue without callback


def get_tkinter_manager():
    """Get the global tkinter manager instance."""
    try:
        try:
            return _get_or_create_manager()
        except:
            return _create_fallback_manager()
    except Exception as e:
        print(f"⚠️ Warning: Manager creation failed: {e}")
        # Return a minimal fallback manager
        return _create_fallback_manager()


def is_gui_backend(backend_name):
    """Check if a backend is a GUI backend."""
    gui_backends = ['TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg', 'MacOSX', 'WXAgg']
    return backend_name in gui_backends


def get_safe_backend():
    """Get a safe non-GUI backend for the current system."""
    # Try backends in order of preference
    safe_backends = ['Agg', 'svg', 'pdf', 'ps']

    for backend in safe_backends:
        try:
            matplotlib.use(backend, force=True)
            return backend
        except:
            continue

    # Fallback to Agg
    return 'Agg'


# Initialize warning suppression on import - with safe initialization
try:
    # Try to get the manager and suppress warnings
    manager = get_tkinter_manager()
    if hasattr(manager, '_suppress_all_gui_warnings'):
        manager._suppress_all_gui_warnings()
except Exception as e:
    # If initialization fails, continue without breaking the import
    print(f"⚠️ Warning: Tkinter manager initialization failed: {e}")
    # This is non-critical, so we continue
