"""
Cross-validation utilities for MWLT Log Prediction with data leakage prevention.

This module provides specialized cross-validation functionality for well log data
that ensures proper preprocessing pipeline management and prevents data leakage.
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import KFold, TimeSeriesSplit
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from typing import Dict, List, Tuple, Optional, Any, Union
import matplotlib
import matplotlib.pyplot as plt
import os
import warnings
import gc
import threading
import sys

# Import the enhanced tkinter manager
try:
    from .tkinter_manager import safe_cross_validation_context, cleanup_tkinter_objects, get_tkinter_manager
    TKINTER_MANAGER_AVAILABLE = True
except ImportError:
    try:
        # Try absolute import
        from utils.tkinter_manager import safe_cross_validation_context, cleanup_tkinter_objects, get_tkinter_manager
        TKINTER_MANAGER_AVAILABLE = True
    except ImportError:
        TKINTER_MANAGER_AVAILABLE = False
        print("WARNING: Enhanced tkinter manager not available, using fallback methods")

# Enhanced matplotlib backend management with tkinter object cleanup
# This prevents tkinter "main thread is not in main loop" errors
def _cleanup_matplotlib_objects():
    """Clean up matplotlib objects that might hold tkinter references."""
    try:
        # Close all existing figures to prevent tkinter object retention
        plt.close('all')

        # Force garbage collection to clean up any remaining objects
        gc.collect()

        # Clear matplotlib's internal caches that might hold GUI references
        if hasattr(matplotlib, '_get_cachedir'):
            try:
                matplotlib.font_manager._rebuild()
            except:
                pass  # Ignore font manager errors

    except Exception as e:
        # Silently handle cleanup errors to avoid disrupting the main process
        pass

def _configure_matplotlib_backend():
    """Configure matplotlib to use a non-GUI backend for cross-validation."""
    current_backend = matplotlib.get_backend()

    # Only change backend if it's a GUI backend
    gui_backends = ['TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg', 'MacOSX']

    if current_backend in gui_backends:
        # Clean up any existing matplotlib objects before switching
        _cleanup_matplotlib_objects()

        # Disable interactive mode to prevent GUI initialization
        plt.ioff()

        # Use Agg backend (non-GUI) for cross-validation
        matplotlib.use('Agg', force=True)
        print(f"[BACKEND] Switched matplotlib backend from {current_backend} to Agg for cross-validation")
        return current_backend

    return None

def _restore_matplotlib_backend(original_backend):
    """Restore the original matplotlib backend after cross-validation."""
    if original_backend:
        # Clean up before restoring to prevent object conflicts
        _cleanup_matplotlib_objects()

        # Restore the original backend
        matplotlib.use(original_backend, force=True)
        print(f"[BACKEND] Restored matplotlib backend to {original_backend}")

        # Re-enable interactive mode if restoring to a GUI backend
        gui_backends = ['TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg', 'MacOSX']
        if original_backend in gui_backends:
            plt.ion()

# Suppress matplotlib warnings during backend switching
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def _safe_matplotlib_operation(func, *args, **kwargs):
    """
    Safely execute matplotlib operations with error handling for GUI issues.

    Args:
        func: Function to execute
        *args: Function arguments
        **kwargs: Function keyword arguments

    Returns:
        Function result or None if error occurred
    """
    try:
        return func(*args, **kwargs)
    except RuntimeError as e:
        if "main thread is not in main loop" in str(e):
            print(f"WARNING: GUI operation skipped due to threading issue: {e}")
            return None
        else:
            raise
    except Exception as e:
        print(f"WARNING: Matplotlib operation failed: {e}")
        return None

def _suppress_gui_warnings():
    """Suppress common GUI-related warnings during cross-validation."""
    warnings.filterwarnings('ignore', message='.*main thread is not in main loop.*')
    warnings.filterwarnings('ignore', message='.*Tkinter.*')
    warnings.filterwarnings('ignore', message='.*tkinter.*')
    warnings.filterwarnings('ignore', category=RuntimeWarning, module='matplotlib')
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

    # Suppress specific tkinter destructor warnings
    warnings.filterwarnings('ignore', message='.*Image.*')
    warnings.filterwarnings('ignore', message='.*Variable.*')
    warnings.filterwarnings('ignore', message='.*PhotoImage.*')

    # Set environment variable to prevent GUI initialization
    os.environ['MPLBACKEND'] = 'Agg'

def _setup_thread_safe_environment():
    """Set up a thread-safe environment for cross-validation."""
    # Ensure we're using a thread-safe backend
    if matplotlib.get_backend() not in ['Agg', 'svg', 'pdf', 'ps']:
        matplotlib.use('Agg', force=True)

    # Disable interactive mode completely
    plt.ioff()

    # Set thread-safe matplotlib parameters
    matplotlib.rcParams['backend'] = 'Agg'
    matplotlib.rcParams['interactive'] = False

    # Prevent any GUI-related imports
    sys.modules.pop('tkinter', None)
    sys.modules.pop('_tkinter', None)

def safe_cross_validation_wrapper(cv_func, *args, **kwargs):
    """
    Enhanced wrapper for cross-validation functions that handles GUI-related errors.

    This wrapper uses the advanced tkinter manager for comprehensive error prevention:
    1. Configures matplotlib to use non-GUI backend
    2. Cleans up tkinter objects before backend switching
    3. Suppresses GUI warnings comprehensively
    4. Handles tkinter threading errors gracefully
    5. Restores original configuration after completion

    Args:
        cv_func: Cross-validation function to wrap
        *args: Function arguments
        **kwargs: Function keyword arguments

    Returns:
        Function result with error handling
    """
    if TKINTER_MANAGER_AVAILABLE:
        # Use the enhanced tkinter manager
        try:
            with safe_cross_validation_context():
                result = cv_func(*args, **kwargs)
                return result
        except Exception as e:
            error_str = str(e)
            if ("main thread is not in main loop" in error_str or
                "tkinter" in error_str.lower() or
                "_tkinter_manager" in error_str):
                print(f"[TKINTER-FIX] Handled tkinter threading error with enhanced manager")
                print(f"   Error: {e}")
                print(f"   Applying emergency recovery...")

                try:
                    # Emergency cleanup and retry
                    cleanup_tkinter_objects()
                    result = cv_func(*args, **kwargs)
                    print(f"[SUCCESS] Cross-validation recovered successfully")
                    return result
                except Exception as retry_error:
                    print(f"[ERROR] Cross-validation failed even with emergency recovery: {retry_error}")
                    return _create_fallback_result(str(retry_error))
            else:
                # For non-tkinter errors, still return a fallback to prevent complete failure
                print(f"[ERROR] Cross-validation failed with non-tkinter error: {e}")
                return _create_fallback_result(str(e))
    else:
        # Fallback to original method
        return _fallback_safe_wrapper(cv_func, *args, **kwargs)


def _create_fallback_result(error_msg="Unknown error"):
    """Create a fallback result when cross-validation fails completely."""
    return {
        'mae_mean': float('inf'), 'mae_std': 0.0,
        'r2_mean': -float('inf'), 'r2_std': 0.0,
        'rmse_mean': float('inf'), 'rmse_std': 0.0,
        'individual_scores': {'mae': [], 'r2': [], 'rmse': []},
        'valid_folds': 0,
        'error': error_msg
    }


def _fallback_safe_wrapper(cv_func, *args, **kwargs):
    """Fallback wrapper when enhanced tkinter manager is not available."""
    # Store original configuration
    original_backend = _configure_matplotlib_backend()
    original_mplbackend = os.environ.get('MPLBACKEND', '')
    original_interactive = matplotlib.is_interactive()

    try:
        # Configure comprehensive safe environment
        _suppress_gui_warnings()
        _setup_thread_safe_environment()
        os.environ['MPLBACKEND'] = 'Agg'

        # Execute the cross-validation function
        result = cv_func(*args, **kwargs)
        return result

    except Exception as e:
        error_str = str(e)
        if ("main thread is not in main loop" in error_str or
            "tkinter" in error_str.lower() or
            "_tkinter_manager" in error_str):
            print(f"[TKINTER-FIX] Handled tkinter threading error during cross-validation")
            print(f"   Error: {e}")
            print(f"   Applying enhanced safety measures...")

            # Enhanced recovery attempt
            try:
                # Force cleanup of all matplotlib objects
                _cleanup_matplotlib_objects()

                # Ensure we're in the safest possible state
                matplotlib.use('Agg', force=True)
                plt.ioff()

                # Clear any remaining GUI references
                gc.collect()

                # Retry the function
                result = cv_func(*args, **kwargs)
                print(f"[SUCCESS] Cross-validation recovered successfully")
                return result

            except Exception as retry_error:
                print(f"[ERROR] Cross-validation failed even with enhanced safety measures: {retry_error}")
                return _create_fallback_result(str(retry_error))
        else:
            # For non-tkinter errors, still return a fallback to prevent complete failure
            print(f"[ERROR] Cross-validation failed with non-tkinter error: {e}")
            return _create_fallback_result(str(e))

    finally:
        # Comprehensive cleanup and restoration
        try:
            # Clean up before restoring
            _cleanup_matplotlib_objects()

            # Restore original configuration
            _restore_matplotlib_backend(original_backend)

            # Restore environment variables
            if original_mplbackend:
                os.environ['MPLBACKEND'] = original_mplbackend
            else:
                os.environ.pop('MPLBACKEND', None)

            # Restore interactive mode if it was originally enabled
            if original_interactive:
                plt.ion()

        except Exception as cleanup_error:
            # Don't let cleanup errors affect the main result
            print(f"WARNING: Cleanup error (non-critical): {cleanup_error}")


class WellLogCrossValidator:
    """
    Cross-validation strategy for well log data that prevents data leakage.
    Supports both K-fold and time series cross-validation.
    """
    
    def __init__(self, cv_strategy: str = 'kfold', n_splits: int = 5, random_state: int = 42):
        """
        Initialize cross-validator with enhanced tkinter error prevention.

        Parameters:
        -----------
        cv_strategy : str, default='kfold'
            Cross-validation strategy ('kfold' or 'timeseries')
        n_splits : int, default=5
            Number of splits for cross-validation
        random_state : int, default=42
            Random state for reproducible results
        """
        self.cv_strategy = cv_strategy
        self.n_splits = n_splits
        self.random_state = random_state

        # Set up thread-safe environment during initialization
        _suppress_gui_warnings()
        _setup_thread_safe_environment()
        
    def get_cv_splits(self, X: pd.DataFrame, y: pd.Series, well_data: Optional[pd.DataFrame] = None):
        """
        Generate cross-validation splits appropriate for well log data.
        
        Parameters:
        -----------
        X : pd.DataFrame
            Feature data
        y : pd.Series
            Target data
        well_data : pd.DataFrame, optional
            Additional well information
            
        Returns:
        --------
        generator
            Cross-validation split generator
        """
        if self.cv_strategy == 'timeseries':
            # Use TimeSeriesSplit for depth-ordered data
            cv = TimeSeriesSplit(n_splits=self.n_splits)
        else:
            # Standard K-fold for mixed well data
            cv = KFold(n_splits=self.n_splits, shuffle=True, random_state=self.random_state)
            
        return cv.split(X, y)
    
    def validate_model(self, model: Any, X: pd.DataFrame, y: pd.Series,
                      preprocessing_pipeline: Optional[Pipeline] = None) -> Dict[str, Any]:
        """
        Perform cross-validation with enhanced tkinter error prevention.

        Parameters:
        -----------
        model : sklearn estimator
            Model to validate
        X : pd.DataFrame
            Feature data
        y : pd.Series
            Target data
        preprocessing_pipeline : Pipeline, optional
            Custom preprocessing pipeline

        Returns:
        --------
        dict
            Cross-validation results with metrics and statistics
        """
        # Enhanced matplotlib backend configuration
        original_backend = _configure_matplotlib_backend()
        original_interactive = matplotlib.is_interactive()

        try:
            # Ensure thread-safe environment
            _setup_thread_safe_environment()

            scores = {'mae': [], 'r2': [], 'rmse': []}
            cv_splits = self.get_cv_splits(X, y)

            for fold_idx, (train_idx, val_idx) in enumerate(cv_splits, 1):
                print(f"   Fold {fold_idx}/{self.n_splits}...")

                try:
                    X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                    y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

                    # Create pipeline to prevent data leakage
                    if preprocessing_pipeline is None:
                        preprocessing_pipeline = Pipeline([
                            ('scaler', StandardScaler()),
                        ])

                    # Create full pipeline with model
                    pipeline = Pipeline([
                        ('preprocessing', preprocessing_pipeline),
                        ('model', model)
                    ])

                    # Handle missing values properly
                    X_train_processed = X_train.ffill().bfill()
                    X_val_processed = X_val.ffill().bfill()

                    # Fit and predict with error handling
                    pipeline.fit(X_train_processed, y_train)
                    y_pred = pipeline.predict(X_val_processed)

                    # Calculate metrics
                    mae = mean_absolute_error(y_val, y_pred)
                    r2 = r2_score(y_val, y_pred)
                    rmse = np.sqrt(mean_squared_error(y_val, y_pred))

                    scores['mae'].append(mae)
                    scores['r2'].append(r2)
                    scores['rmse'].append(rmse)

                except Exception as fold_error:
                    print(f"   WARNING: Fold {fold_idx} failed: {fold_error}")
                    # Add default scores to prevent complete failure
                    scores['mae'].append(float('inf'))
                    scores['r2'].append(-float('inf'))
                    scores['rmse'].append(float('inf'))

            # Calculate mean and std, filtering out infinite values
            valid_mae = [x for x in scores['mae'] if np.isfinite(x)]
            valid_r2 = [x for x in scores['r2'] if np.isfinite(x)]
            valid_rmse = [x for x in scores['rmse'] if np.isfinite(x)]

            cv_results = {
                'mae_mean': np.mean(valid_mae) if valid_mae else float('inf'),
                'mae_std': np.std(valid_mae) if valid_mae else 0.0,
                'r2_mean': np.mean(valid_r2) if valid_r2 else -float('inf'),
                'r2_std': np.std(valid_r2) if valid_r2 else 0.0,
                'rmse_mean': np.mean(valid_rmse) if valid_rmse else float('inf'),
                'rmse_std': np.std(valid_rmse) if valid_rmse else 0.0,
                'individual_scores': scores,
                'valid_folds': len(valid_mae)
            }

            return cv_results

        except Exception as e:
            print(f"WARNING: Cross-validation error: {e}")
            # Return minimal results to prevent complete failure
            return {
                'mae_mean': float('inf'), 'mae_std': 0.0,
                'r2_mean': -float('inf'), 'r2_std': 0.0,
                'rmse_mean': float('inf'), 'rmse_std': 0.0,
                'individual_scores': {'mae': [], 'r2': [], 'rmse': []},
                'valid_folds': 0,
                'error': str(e)
            }

        finally:
            # Enhanced cleanup and restoration
            try:
                _cleanup_matplotlib_objects()
                _restore_matplotlib_backend(original_backend)
                if original_interactive:
                    plt.ion()
            except Exception as cleanup_error:
                print(f"WARNING: Cleanup warning: {cleanup_error}")


def determine_cv_strategy(df: pd.DataFrame, feature_cols: List[str], well_cfg: Dict[str, Any]) -> str:
    """
    Determine the optimal cross-validation strategy based on data characteristics.
    
    Parameters:
    -----------
    df : pd.DataFrame
        Input dataframe
    feature_cols : List[str]
        List of feature columns
    well_cfg : Dict[str, Any]
        Well configuration dictionary
        
    Returns:
    --------
    str
        CV strategy ('timeseries' or 'kfold')
    """
    # Check if MD (Measured Depth) is available and data has depth ordering
    has_depth = 'MD' in df.columns
    is_mixed_wells = well_cfg['mode'] == 'mixed'
    
    # Check if data is depth-ordered within wells
    depth_ordered = False
    if has_depth and is_mixed_wells:
        wells = df['WELL'].unique()
        for well in wells[:3]:  # Check first 3 wells
            well_data = df[df['WELL'] == well]['MD']
            if len(well_data) > 1:
                # Check if MD is mostly increasing (allowing for some noise)
                increasing_ratio = np.mean(np.diff(well_data) > 0)
                if increasing_ratio > 0.7:  # 70% of depth changes are increasing
                    depth_ordered = True
                    break
    
    if depth_ordered and is_mixed_wells:
        return 'timeseries'
    else:
        return 'kfold'


def create_preprocessing_pipeline() -> Pipeline:
    """
    Create a standard preprocessing pipeline for well log data.
    
    Returns:
    --------
    Pipeline
        Sklearn preprocessing pipeline
    """
    return Pipeline([
        ('scaler', StandardScaler()),
    ])


def evaluate_models_cv(models_dict: Dict[str, Any], X: pd.DataFrame, y: pd.Series,
                      cv_strategy: str = 'kfold', n_splits: int = 5) -> List[Dict[str, Any]]:
    """
    Evaluate multiple models using cross-validation.

    Parameters:
    -----------
    models_dict : dict
        Dictionary of model_name: model pairs
    X : pd.DataFrame
        Feature data
    y : pd.Series
        Target data
    cv_strategy : str, default='kfold'
        Cross-validation strategy
    n_splits : int, default=5
        Number of CV splits

    Returns:
    --------
    list
        List of evaluation results for each model
    """
    # Configure matplotlib backend to prevent tkinter errors
    original_backend = _configure_matplotlib_backend()

    try:
        cv_validator = WellLogCrossValidator(cv_strategy=cv_strategy, n_splits=n_splits)
        evaluations = []

        for model_name, model in models_dict.items():
            print(f"\n🔄 Cross-validating {model_name}...")

            # Perform cross-validation
            cv_results = cv_validator.validate_model(model, X, y)

            print(f"   MAE: {cv_results['mae_mean']:.3f} ± {cv_results['mae_std']:.3f}")
            print(f"   R²:  {cv_results['r2_mean']:.3f} ± {cv_results['r2_std']:.3f}")
            print(f"   RMSE: {cv_results['rmse_mean']:.3f} ± {cv_results['rmse_std']:.3f}")

            # Store results
            evaluation = {
                'model_name': model_name,
                'mae': cv_results['mae_mean'],
                'mae_std': cv_results['mae_std'],
                'r2': cv_results['r2_mean'],
                'r2_std': cv_results['r2_std'],
                'rmse': cv_results['rmse_mean'],
                'rmse_std': cv_results['rmse_std'],
                'composite_score': cv_results['mae_mean'] * 0.4 + (1 - cv_results['r2_mean']) * 0.6,
                'cv_results': cv_results
            }
            evaluations.append(evaluation)

        return evaluations

    finally:
        # Always restore the original matplotlib backend
        _restore_matplotlib_backend(original_backend)


def get_best_model(evaluations: List[Dict[str, Any]]) -> Tuple[str, Dict[str, Any]]:
    """
    Get the best model based on composite score.
    
    Parameters:
    -----------
    evaluations : list
        List of model evaluation results
        
    Returns:
    --------
    tuple
        Best model name and its evaluation results
    """
    best_eval = min(evaluations, key=lambda x: x['composite_score'])
    return best_eval['model_name'], best_eval


def print_cv_summary(evaluations: List[Dict[str, Any]], cv_strategy: str):
    """
    Print a summary of cross-validation results.
    
    Parameters:
    -----------
    evaluations : list
        List of model evaluation results
    cv_strategy : str
        Cross-validation strategy used
    """
    print("\n" + "="*60)
    print("CROSS-VALIDATION PERFORMANCE SUMMARY")
    print("="*60)
    print(f"CV Strategy: {cv_strategy.upper()}")
    print(f"Models Evaluated: {len(evaluations)}")
    
    print(f"\nModel Performance (Mean ± Std):")
    print("-" * 50)
    
    # Sort by composite score (best first)
    sorted_evals = sorted(evaluations, key=lambda x: x['composite_score'])
    
    for i, eval_result in enumerate(sorted_evals, 1):
        model_name = eval_result['model_name']
        mae = eval_result['mae']
        mae_std = eval_result['mae_std']
        r2 = eval_result['r2']
        r2_std = eval_result['r2_std']
        rmse = eval_result['rmse']
        rmse_std = eval_result['rmse_std']
        
        print(f"{i}. {model_name}:")
        print(f"   MAE:  {mae:.3f} ± {mae_std:.3f}")
        print(f"   R²:   {r2:.3f} ± {r2_std:.3f}")
        print(f"   RMSE: {rmse:.3f} ± {rmse_std:.3f}")
        print()
    
    best_model = sorted_evals[0]['model_name']
    print(f"🏆 Best Model: {best_model}")
    print("="*60)