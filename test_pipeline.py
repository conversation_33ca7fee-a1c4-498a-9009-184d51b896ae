#!/usr/bin/env python3
"""
Pipeline Verification Test
Tests all core imports and functions after cleanup
"""

def test_imports():
    """Test all critical imports"""
    print("[TEST] Testing pipeline imports...")
    
    try:
        # Test data handler imports
        from preprocessing.data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
        print("  [SUCCESS] data_handler imports successful")
        
        # Test config handler imports
        from config_handler import (get_input_files, select_output_directory, configure_log_selection,
                                  configure_well_separation, get_prediction_mode, configure_hyperparameters,
                                  console_select, configure_gpu_optimization,
                                  apply_gpu_optimizations_to_hparams, apply_data_sufficiency_optimizations)
        print("  [SUCCESS] config_handler imports successful")
        
        # Test ml_core imports
        from preprocessing.ml_core import impute_logs, impute_logs_deep, MODEL_REGISTRY
        print("  [SUCCESS] ml_core imports successful")
        
        # Test reporting imports
        from reporting import (generate_qc_report, create_summary_plots, generate_final_report,
                              create_multi_model_comparison_plots, create_separate_comparison_plots,
                              create_crossplot_analysis, create_model_ranking_visualization)
        print("  [SUCCESS] reporting imports successful")
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_registry():
    """Test MODEL_REGISTRY functionality"""
    print("\n[TEST] Testing MODEL_REGISTRY...")
    
    try:
        from preprocessing.ml_core import MODEL_REGISTRY
        
        print(f"  [SUCCESS] Available models: {list(MODEL_REGISTRY.keys())}")
        
        # Check each model has required keys
        for model_key, config in MODEL_REGISTRY.items():
            required_keys = ['name', 'model_class', 'type', 'hyperparameters']
            missing_keys = [key for key in required_keys if key not in config]
            if missing_keys:
                print(f"  [WARNING] {model_key} missing keys: {missing_keys}")
            else:
                print(f"  [SUCCESS] {model_key} configuration valid")
                
        return True
        
    except Exception as e:
        print(f"  [ERROR] MODEL_REGISTRY error: {e}")
        return False

def test_core_functions():
    """Test core pipeline functions"""
    print("\n[TEST] Testing core functions...")
    
    try:
        from config_handler import configure_hyperparameters
        from preprocessing.ml_core import MODEL_REGISTRY
        
        # Test hyperparameter configuration
        hparams = configure_hyperparameters()
        print(f"  [SUCCESS] Hyperparameters configured for {len(hparams)} models")
        
        # Verify each model in registry has hyperparameters
        for model_key in MODEL_REGISTRY.keys():
            if model_key in hparams:
                print(f"  [SUCCESS] {model_key} has hyperparameters")
            else:
                print(f"  [WARNING] {model_key} missing hyperparameters")
                
        return True
        
    except Exception as e:
        print(f"  [ERROR] Core function error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all verification tests"""
    print("[PIPELINE] Pipeline Verification Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Model Registry Test", test_model_registry), 
        ("Core Functions Test", test_core_functions)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("[SUMMARY] VERIFICATION SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"  {status}: {test_name}")
        if not result:
            all_passed = False
    
    print(f"\n[STATUS] Overall Status: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    main()
