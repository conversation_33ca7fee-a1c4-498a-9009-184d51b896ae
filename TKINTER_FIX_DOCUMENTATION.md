# Tkinter Threading Error Fix for Cross-Validation

## Problem Description

The machine learning pipeline was experiencing tkinter-related runtime errors during cross-validation of K-Nearest Neighbors and other models. These errors occurred when matplotlib switched backends from TkAgg to Agg during cross-validation, causing "RuntimeError: main thread is not in main loop" exceptions in tkin<PERSON>'s Image and Variable destructors.

### Specific Error Messages
- `RuntimeError: main thread is not in main loop`
- Tkinter Image destructor errors
- Tkinter Variable destructor errors
- Threading violations during matplotlib backend switching

## Root Cause Analysis

1. **Main Thread Initialization**: The application starts with TkAgg backend, which initializes tkinter in the main thread
2. **Backend Switching**: During cross-validation, the backend switches to Agg (non-GUI) to prevent GUI interference
3. **Object Destruction**: When matplotlib objects are garbage collected, tkinter objects try to destroy themselves outside the main thread
4. **Threading Violation**: This causes runtime errors because tkinter objects must be destroyed in the same thread where they were created

## Solution Implementation

### 1. Enhanced Cross-Validation Utilities (`utils/crossvalidation_utils.py`)

**Key Improvements:**
- **Comprehensive Object Cleanup**: Added `_cleanup_matplotlib_objects()` function that properly closes all figures and forces garbage collection before backend switching
- **Thread-Safe Environment Setup**: Added `_setup_thread_safe_environment()` function that ensures matplotlib is in a safe state for cross-validation
- **Enhanced Error Recovery**: Improved `safe_cross_validation_wrapper()` with multiple recovery attempts and fallback results
- **Proper Backend Restoration**: Enhanced backend restoration with cleanup before switching back

**Code Changes:**
```python
def _cleanup_matplotlib_objects():
    """Clean up matplotlib objects that might hold tkinter references."""
    try:
        plt.close('all')  # Close all existing figures
        gc.collect()      # Force garbage collection
        # Clear matplotlib's internal caches
        if hasattr(matplotlib, '_get_cachedir'):
            try:
                matplotlib.font_manager._rebuild()
            except:
                pass
    except Exception:
        pass  # Silently handle cleanup errors

def _setup_thread_safe_environment():
    """Set up a thread-safe environment for cross-validation."""
    if matplotlib.get_backend() not in ['Agg', 'svg', 'pdf', 'ps']:
        matplotlib.use('Agg', force=True)
    plt.ioff()  # Disable interactive mode
    matplotlib.rcParams['backend'] = 'Agg'
    matplotlib.rcParams['interactive'] = False
```

### 2. Advanced Tkinter Manager (`utils/tkinter_manager.py`)

**New Features:**
- **TkinterObjectManager Class**: Comprehensive management of tkinter objects and backend switching
- **Context Manager**: `safe_cross_validation_context()` for automatic setup and cleanup
- **Aggressive Cleanup**: `force_cleanup_tkinter_objects()` method that aggressively cleans up tkinter references
- **Warning Suppression**: Comprehensive suppression of GUI-related warnings

**Key Methods:**
```python
class TkinterObjectManager:
    def setup_safe_backend(self, target_backend='Agg'):
        """Set up a safe matplotlib backend for cross-validation."""
        self.original_backend = matplotlib.get_backend()
        self.force_cleanup_tkinter_objects()
        plt.ioff()
        matplotlib.use(target_backend, force=True)
        
    def restore_original_backend(self):
        """Restore the original matplotlib backend and settings."""
        self.force_cleanup_tkinter_objects()
        matplotlib.use(self.original_backend, force=True)
        if self.original_interactive:
            plt.ion()
```

### 3. Enhanced WellLogCrossValidator

**Improvements:**
- **Thread-Safe Initialization**: Sets up safe environment during validator initialization
- **Per-Fold Error Handling**: Individual fold failures don't crash the entire cross-validation
- **Robust Metrics Calculation**: Filters out infinite values and handles partial failures
- **Enhanced Cleanup**: Comprehensive cleanup in finally blocks

## Usage

### Automatic Protection
The enhanced cross-validation utilities automatically protect against tkinter errors:

```python
from utils.crossvalidation_utils import safe_cross_validation_wrapper, WellLogCrossValidator

# This is now protected against tkinter errors
cv_validator = WellLogCrossValidator(cv_strategy='kfold', n_splits=5)
cv_results = safe_cross_validation_wrapper(cv_validator.validate_model, model, X, y)
```

### Manual Protection
For custom cross-validation code:

```python
from utils.tkinter_manager import safe_cross_validation_context

with safe_cross_validation_context():
    # Your cross-validation code here
    # Backend is automatically switched to Agg and restored afterward
    results = your_cross_validation_function()
```

## Benefits

1. **Error Elimination**: Prevents "main thread is not in main loop" errors
2. **Robust Recovery**: Multiple recovery mechanisms for different failure scenarios
3. **Maintained Functionality**: All cross-validation functionality preserved
4. **Automatic Restoration**: Original matplotlib settings are properly restored
5. **Performance**: Minimal performance impact with efficient cleanup
6. **Compatibility**: Works with all existing models and cross-validation strategies

## Technical Details

### Backend Switching Strategy
1. **Pre-Switch Cleanup**: Close all figures and force garbage collection
2. **Safe Backend Switch**: Switch to Agg backend with proper configuration
3. **Environment Setup**: Disable interactive mode and set thread-safe parameters
4. **Post-Execution Cleanup**: Clean up before restoring original backend
5. **Restoration**: Restore original backend and interactive mode

### Error Handling Hierarchy
1. **Primary Protection**: Enhanced cross-validation wrapper with comprehensive setup
2. **Secondary Recovery**: Error-specific recovery attempts with additional cleanup
3. **Fallback Results**: Graceful degradation with meaningful error information
4. **Silent Cleanup**: Non-critical cleanup errors don't affect main functionality

### Warning Suppression
Comprehensive suppression of GUI-related warnings:
- Tkinter threading warnings
- Matplotlib backend warnings
- Image and Variable destructor warnings
- Display-related warnings

## Testing

The solution includes comprehensive test scripts:
- `test_simple_tkinter_fix.py`: Basic functionality testing
- `utils/tkinter_fix_test.py`: Original test script
- Backend switching verification
- Cross-validation safety testing
- Stress testing with multiple models

## Backward Compatibility

The solution is fully backward compatible:
- Existing code continues to work without changes
- Original functionality is preserved
- Performance impact is minimal
- No breaking changes to APIs

## Future Considerations

1. **Monitoring**: Consider adding optional logging for debugging
2. **Performance**: Monitor for any performance impacts in production
3. **Updates**: Keep solution updated with matplotlib and tkinter changes
4. **Testing**: Regular testing with new model types and configurations
