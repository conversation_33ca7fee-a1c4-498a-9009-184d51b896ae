#!/usr/bin/env python3
"""
Windows Compatibility Module for Signal-Based Timeouts
Provides cross-platform timeout mechanisms and prevents SIGALRM errors on Windows.
"""

import platform
import threading
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError
from typing import Callable, Any, Optional

# Platform detection
IS_WINDOWS = platform.system() == 'Windows'

class WindowsCompatibleTimeout:
    """Cross-platform timeout mechanism that works on Windows and Unix systems."""
    
    def __init__(self, timeout_seconds: int = 30):
        self.timeout_seconds = timeout_seconds
        self.is_windows = IS_WINDOWS
    
    def run_with_timeout(self, func: Callable, *args, **kwargs) -> Any:
        """Run a function with timeout using cross-platform mechanism."""
        if self.is_windows:
            return self._run_with_thread_timeout(func, *args, **kwargs)
        else:
            # On Unix systems, we can still use ThreadPoolExecutor for consistency
            return self._run_with_thread_timeout(func, *args, **kwargs)
    
    def _run_with_thread_timeout(self, func: Callable, *args, **kwargs) -> Any:
        """Use ThreadPoolExecutor for timeout (works on all platforms)."""
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(func, *args, **kwargs)
            try:
                return future.result(timeout=self.timeout_seconds)
            except FuturesTimeoutError:
                raise TimeoutError(f"Function timed out after {self.timeout_seconds} seconds")

# Monkey patch any potential signal-based timeout code
if IS_WINDOWS:
    import sys
    
    # Create a mock signal module for Windows compatibility
    class MockSignal:
        """Mock signal module that prevents SIGALRM errors on Windows."""
        
        def __init__(self):
            # Common signal constants (Windows-compatible ones)
            self.SIGTERM = 15
            self.SIGINT = 2
            # SIGALRM is not available on Windows, so we provide a dummy
            self.SIGALRM = None
        
        def signal(self, sig, handler):
            """Mock signal handler that does nothing for SIGALRM on Windows."""
            if sig == self.SIGALRM:
                print("Warning: SIGALRM not supported on Windows. Using alternative timeout mechanism.")
                return None
            # For other signals, we could implement actual handling if needed
            return None
        
        def alarm(self, seconds):
            """Mock alarm function that does nothing on Windows."""
            print(f"Warning: signal.alarm({seconds}) not supported on Windows. Using alternative timeout mechanism.")
            return 0
    
    # Replace signal module in sys.modules if it's being used
    if 'signal' in sys.modules:
        original_signal = sys.modules['signal']
        # Only replace if the original signal module doesn't have SIGALRM
        if not hasattr(original_signal, 'SIGALRM'):
            sys.modules['signal'] = MockSignal()

def ensure_windows_compatibility():
    """Ensure Windows compatibility for signal-based operations."""
    if IS_WINDOWS:
        print("Windows detected. Signal-based timeouts will use thread-based alternatives.")
        return True
    return False

# Utility function for safe timeout operations
def safe_timeout_call(func: Callable, timeout_seconds: int = 30, *args, **kwargs) -> Any:
    """Safely call a function with timeout, using Windows-compatible mechanism."""
    timeout_manager = WindowsCompatibleTimeout(timeout_seconds)
    return timeout_manager.run_with_timeout(func, *args, **kwargs)

# Initialize Windows compatibility on import
ensure_windows_compatibility()
