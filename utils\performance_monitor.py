"""
Performance Monitoring Utilities for ML Log Prediction
Provides GPU utilization monitoring and performance benchmarking capabilities.
"""

import time
import psutil
import torch
import numpy as np
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import threading
import queue

class PerformanceMonitor:
    """
    Comprehensive performance monitoring for ML training and inference.
    Tracks GPU utilization, memory usage, and training metrics.
    """
    
    def __init__(self, enable_gpu_monitoring=True, monitoring_interval=1.0):
        """
        Initialize performance monitor.
        
        Args:
            enable_gpu_monitoring: Whether to monitor GPU metrics
            monitoring_interval: Interval between monitoring samples (seconds)
        """
        self.enable_gpu_monitoring = enable_gpu_monitoring and torch.cuda.is_available()
        self.monitoring_interval = monitoring_interval
        self.monitoring_active = False
        self.monitoring_thread = None
        self.metrics_queue = queue.Queue()
        
        # Performance metrics storage
        self.metrics_history = {
            'timestamps': [],
            'cpu_percent': [],
            'memory_percent': [],
            'gpu_utilization': [],
            'gpu_memory_used': [],
            'gpu_memory_total': [],
            'gpu_temperature': []
        }
        
        # Training metrics
        self.training_metrics = {
            'epoch_times': [],
            'batch_times': [],
            'loss_values': [],
            'learning_rates': []
        }
        
        print(f"[PERF] Performance Monitor initialized")
        if self.enable_gpu_monitoring:
            print(f"   • GPU monitoring enabled")
        print(f"   • Monitoring interval: {monitoring_interval}s")
    
    def start_monitoring(self):
        """Start background monitoring of system resources."""
        if self.monitoring_active:
            print("⚠️ Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        print("📊 Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring."""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        print("📊 Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Background monitoring loop."""
        while self.monitoring_active:
            try:
                timestamp = time.time()
                
                # CPU and system memory
                cpu_percent = psutil.cpu_percent()
                memory_info = psutil.virtual_memory()
                memory_percent = memory_info.percent
                
                # GPU metrics
                gpu_util = 0
                gpu_memory_used = 0
                gpu_memory_total = 0
                gpu_temp = 0
                
                if self.enable_gpu_monitoring:
                    try:
                        # GPU utilization (approximation using memory usage)
                        gpu_memory_used = torch.cuda.memory_allocated() / 1e9  # GB
                        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1e9  # GB
                        gpu_util = (gpu_memory_used / gpu_memory_total) * 100 if gpu_memory_total > 0 else 0
                        
                        # GPU temperature (if available)
                        try:
                            import pynvml
                            pynvml.nvmlInit()
                            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
                            gpu_temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                        except:
                            gpu_temp = 0  # Temperature monitoring not available
                    except Exception as e:
                        pass  # GPU monitoring failed, continue with zeros
                
                # Store metrics
                metrics = {
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'gpu_utilization': gpu_util,
                    'gpu_memory_used': gpu_memory_used,
                    'gpu_memory_total': gpu_memory_total,
                    'gpu_temperature': gpu_temp
                }
                
                # Add to queue (non-blocking)
                try:
                    self.metrics_queue.put_nowait(metrics)
                except queue.Full:
                    pass  # Queue full, skip this sample
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"⚠️ Monitoring error: {e}")
                time.sleep(self.monitoring_interval)
    
    def collect_metrics(self):
        """Collect all queued metrics into history."""
        while not self.metrics_queue.empty():
            try:
                metrics = self.metrics_queue.get_nowait()
                for key, value in metrics.items():
                    if key in self.metrics_history:
                        self.metrics_history[key].append(value)
            except queue.Empty:
                break
    
    @contextmanager
    def monitor_training_epoch(self):
        """Context manager for monitoring a training epoch."""
        start_time = time.time()
        start_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        yield
        
        end_time = time.time()
        end_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        epoch_time = end_time - start_time
        memory_delta = (end_memory - start_memory) / 1e6  # MB
        
        self.training_metrics['epoch_times'].append(epoch_time)
        
        print(f"   ⏱️ Epoch time: {epoch_time:.2f}s, Memory Δ: {memory_delta:+.1f}MB")
    
    @contextmanager
    def monitor_batch(self):
        """Context manager for monitoring a training batch."""
        start_time = time.time()
        
        yield
        
        end_time = time.time()
        batch_time = end_time - start_time
        self.training_metrics['batch_times'].append(batch_time)
    
    def log_training_metrics(self, loss=None, learning_rate=None):
        """Log training metrics."""
        if loss is not None:
            self.training_metrics['loss_values'].append(loss)
        if learning_rate is not None:
            self.training_metrics['learning_rates'].append(learning_rate)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        self.collect_metrics()
        
        summary = {
            'monitoring_duration': 0,
            'cpu_stats': {},
            'memory_stats': {},
            'gpu_stats': {},
            'training_stats': {}
        }
        
        if self.metrics_history['timestamps']:
            duration = max(self.metrics_history['timestamps']) - min(self.metrics_history['timestamps'])
            summary['monitoring_duration'] = duration
            
            # CPU statistics
            if self.metrics_history['cpu_percent']:
                summary['cpu_stats'] = {
                    'avg_utilization': np.mean(self.metrics_history['cpu_percent']),
                    'max_utilization': np.max(self.metrics_history['cpu_percent']),
                    'min_utilization': np.min(self.metrics_history['cpu_percent'])
                }
            
            # Memory statistics
            if self.metrics_history['memory_percent']:
                summary['memory_stats'] = {
                    'avg_usage': np.mean(self.metrics_history['memory_percent']),
                    'max_usage': np.max(self.metrics_history['memory_percent']),
                    'min_usage': np.min(self.metrics_history['memory_percent'])
                }
            
            # GPU statistics
            if self.enable_gpu_monitoring and self.metrics_history['gpu_utilization']:
                summary['gpu_stats'] = {
                    'avg_utilization': np.mean(self.metrics_history['gpu_utilization']),
                    'max_utilization': np.max(self.metrics_history['gpu_utilization']),
                    'avg_memory_used': np.mean(self.metrics_history['gpu_memory_used']),
                    'max_memory_used': np.max(self.metrics_history['gpu_memory_used']),
                    'memory_total': self.metrics_history['gpu_memory_total'][-1] if self.metrics_history['gpu_memory_total'] else 0
                }
                
                if self.metrics_history['gpu_temperature']:
                    temps = [t for t in self.metrics_history['gpu_temperature'] if t > 0]
                    if temps:
                        summary['gpu_stats']['avg_temperature'] = np.mean(temps)
                        summary['gpu_stats']['max_temperature'] = np.max(temps)
        
        # Training statistics
        if self.training_metrics['epoch_times']:
            summary['training_stats'] = {
                'total_epochs': len(self.training_metrics['epoch_times']),
                'avg_epoch_time': np.mean(self.training_metrics['epoch_times']),
                'total_training_time': np.sum(self.training_metrics['epoch_times'])
            }
            
            if self.training_metrics['batch_times']:
                summary['training_stats']['avg_batch_time'] = np.mean(self.training_metrics['batch_times'])
                summary['training_stats']['total_batches'] = len(self.training_metrics['batch_times'])
        
        return summary
    
    def print_performance_report(self):
        """Print a comprehensive performance report."""
        summary = self.get_performance_summary()
        
        print("\n" + "="*60)
        print("📊 PERFORMANCE MONITORING REPORT")
        print("="*60)
        
        if summary['monitoring_duration'] > 0:
            print(f"⏱️ Monitoring Duration: {summary['monitoring_duration']:.1f}s")
            
            # CPU Report
            if summary['cpu_stats']:
                cpu = summary['cpu_stats']
                print(f"\n💻 CPU Performance:")
                print(f"   • Average Utilization: {cpu['avg_utilization']:.1f}%")
                print(f"   • Peak Utilization: {cpu['max_utilization']:.1f}%")
            
            # Memory Report
            if summary['memory_stats']:
                mem = summary['memory_stats']
                print(f"\n🧠 Memory Usage:")
                print(f"   • Average Usage: {mem['avg_usage']:.1f}%")
                print(f"   • Peak Usage: {mem['max_usage']:.1f}%")
            
            # GPU Report
            if summary['gpu_stats']:
                gpu = summary['gpu_stats']
                print(f"\n🚀 GPU Performance:")
                print(f"   • Average Utilization: {gpu['avg_utilization']:.1f}%")
                print(f"   • Peak Utilization: {gpu['max_utilization']:.1f}%")
                print(f"   • Average Memory Used: {gpu['avg_memory_used']:.1f}GB")
                print(f"   • Peak Memory Used: {gpu['max_memory_used']:.1f}GB")
                print(f"   • Total GPU Memory: {gpu['memory_total']:.1f}GB")
                
                if 'avg_temperature' in gpu:
                    print(f"   • Average Temperature: {gpu['avg_temperature']:.1f}°C")
                    print(f"   • Peak Temperature: {gpu['max_temperature']:.1f}°C")
            
            # Training Report
            if summary['training_stats']:
                train = summary['training_stats']
                print(f"\n🎯 Training Performance:")
                print(f"   • Total Epochs: {train['total_epochs']}")
                print(f"   • Average Epoch Time: {train['avg_epoch_time']:.2f}s")
                print(f"   • Total Training Time: {train['total_training_time']:.1f}s")
                
                if 'avg_batch_time' in train:
                    print(f"   • Average Batch Time: {train['avg_batch_time']:.3f}s")
                    print(f"   • Total Batches: {train['total_batches']}")
        else:
            print("⚠️ No monitoring data available")
        
        print("="*60)
    
    def reset_metrics(self):
        """Reset all collected metrics."""
        for key in self.metrics_history:
            self.metrics_history[key].clear()
        
        for key in self.training_metrics:
            self.training_metrics[key].clear()
        
        # Clear queue
        while not self.metrics_queue.empty():
            try:
                self.metrics_queue.get_nowait()
            except queue.Empty:
                break
        
        print("🔄 Performance metrics reset")

# Global performance monitor instance
_global_monitor = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def start_performance_monitoring():
    """Start global performance monitoring."""
    monitor = get_performance_monitor()
    monitor.start_monitoring()

def stop_performance_monitoring():
    """Stop global performance monitoring."""
    monitor = get_performance_monitor()
    monitor.stop_monitoring()

def print_performance_report():
    """Print global performance report."""
    monitor = get_performance_monitor()
    monitor.print_performance_report()


class ModelPerformanceMonitor:
    """
    Enhanced performance monitoring specifically for ML model training and evaluation.
    Includes anomaly detection for suspicious metrics that may indicate data leakage.
    """

    def __init__(self, enable_anomaly_detection=True):
        """
        Initialize model performance monitor.

        Args:
            enable_anomaly_detection: Whether to enable anomaly detection for metrics
        """
        self.enable_anomaly_detection = enable_anomaly_detection
        self.model_metrics_history = []
        self.anomaly_thresholds = {
            'r2_suspicious': 0.95,      # R² above this is suspicious
            'r2_perfect': 0.99,         # R² above this is highly suspicious
            'mae_too_low': 0.001,       # MAE below this is suspicious
            'rmse_too_low': 0.001,      # RMSE below this is suspicious
            'correlation_threshold': 0.95  # Feature-target correlation above this is suspicious
        }

        print("🔍 Model Performance Monitor initialized with anomaly detection")

    def log_model_performance(self, model_name: str, metrics: Dict[str, float],
                            additional_info: Dict[str, Any] = None):
        """
        Log performance metrics for a model and check for anomalies.

        Args:
            model_name: Name of the model
            metrics: Dictionary of performance metrics (mae, r2, rmse, etc.)
            additional_info: Additional information about the model/training
        """
        timestamp = time.time()

        # Create performance record
        performance_record = {
            'timestamp': timestamp,
            'model_name': model_name,
            'metrics': metrics.copy(),
            'additional_info': additional_info or {},
            'anomalies_detected': [],
            'anomaly_score': 0.0
        }

        # Run anomaly detection
        if self.enable_anomaly_detection:
            self._detect_metric_anomalies(performance_record)

        # Store record
        self.model_metrics_history.append(performance_record)

        # Print performance with anomaly warnings
        self._print_model_performance(performance_record)

        return performance_record

    def _detect_metric_anomalies(self, record: Dict[str, Any]):
        """Detect anomalies in model performance metrics."""
        metrics = record['metrics']
        anomalies = []
        anomaly_score = 0.0

        # Check R² anomalies
        if 'r2' in metrics:
            r2 = metrics['r2']
            if r2 > self.anomaly_thresholds['r2_perfect']:
                anomalies.append({
                    'type': 'perfect_r2',
                    'severity': 'critical',
                    'message': f"Perfect R² ({r2:.4f}) - likely data leakage",
                    'value': r2
                })
                anomaly_score += 1.0
            elif r2 > self.anomaly_thresholds['r2_suspicious']:
                anomalies.append({
                    'type': 'suspicious_r2',
                    'severity': 'warning',
                    'message': f"Suspiciously high R² ({r2:.4f}) - potential data leakage",
                    'value': r2
                })
                anomaly_score += 0.5

        # Check MAE anomalies
        if 'mae' in metrics:
            mae = metrics['mae']
            if mae < self.anomaly_thresholds['mae_too_low']:
                anomalies.append({
                    'type': 'perfect_mae',
                    'severity': 'warning',
                    'message': f"Extremely low MAE ({mae:.6f}) - potential overfitting or leakage",
                    'value': mae
                })
                anomaly_score += 0.3

        # Check RMSE anomalies
        if 'rmse' in metrics:
            rmse = metrics['rmse']
            if rmse < self.anomaly_thresholds['rmse_too_low']:
                anomalies.append({
                    'type': 'perfect_rmse',
                    'severity': 'warning',
                    'message': f"Extremely low RMSE ({rmse:.6f}) - potential overfitting or leakage",
                    'value': rmse
                })
                anomaly_score += 0.3

        # Check for impossible combinations
        if 'r2' in metrics and 'mae' in metrics:
            r2, mae = metrics['r2'], metrics['mae']
            if r2 > 0.9 and mae < 0.01:
                anomalies.append({
                    'type': 'impossible_combination',
                    'severity': 'critical',
                    'message': f"Impossible metric combination: R²={r2:.4f}, MAE={mae:.6f}",
                    'value': {'r2': r2, 'mae': mae}
                })
                anomaly_score += 1.0

        # Store anomaly information
        record['anomalies_detected'] = anomalies
        record['anomaly_score'] = anomaly_score

    def _print_model_performance(self, record: Dict[str, Any]):
        """Print model performance with anomaly warnings."""
        model_name = record['model_name']
        metrics = record['metrics']
        anomalies = record['anomalies_detected']

        print(f"\n📊 Model Performance: {model_name}")
        print("-" * 40)

        # Print metrics
        for metric, value in metrics.items():
            if isinstance(value, float):
                print(f"   {metric.upper()}: {value:.4f}")
            else:
                print(f"   {metric.upper()}: {value}")

        # Print anomaly warnings
        if anomalies:
            print("\n🚨 ANOMALY ALERTS:")
            for anomaly in anomalies:
                severity_icon = "🔴" if anomaly['severity'] == 'critical' else "🟡"
                print(f"   {severity_icon} {anomaly['message']}")

            print(f"\n   Overall Anomaly Score: {record['anomaly_score']:.2f}")

            if record['anomaly_score'] >= 1.0:
                print("   🚨 CRITICAL: High probability of data leakage!")
            elif record['anomaly_score'] >= 0.5:
                print("   ⚠️ WARNING: Potential data quality issues")
        else:
            print("   ✅ No anomalies detected")

        print("-" * 40)

    def get_anomaly_summary(self) -> Dict[str, Any]:
        """Get summary of all detected anomalies."""
        total_models = len(self.model_metrics_history)
        models_with_anomalies = sum(1 for record in self.model_metrics_history
                                  if record['anomalies_detected'])

        all_anomalies = []
        for record in self.model_metrics_history:
            for anomaly in record['anomalies_detected']:
                anomaly_copy = anomaly.copy()
                anomaly_copy['model_name'] = record['model_name']
                anomaly_copy['timestamp'] = record['timestamp']
                all_anomalies.append(anomaly_copy)

        # Group anomalies by type
        anomaly_types = {}
        for anomaly in all_anomalies:
            anomaly_type = anomaly['type']
            if anomaly_type not in anomaly_types:
                anomaly_types[anomaly_type] = []
            anomaly_types[anomaly_type].append(anomaly)

        return {
            'total_models_monitored': total_models,
            'models_with_anomalies': models_with_anomalies,
            'anomaly_rate': models_with_anomalies / total_models if total_models > 0 else 0,
            'total_anomalies': len(all_anomalies),
            'anomaly_types': anomaly_types,
            'recommendations': self._generate_anomaly_recommendations(anomaly_types)
        }

    def _generate_anomaly_recommendations(self, anomaly_types: Dict[str, List]) -> List[str]:
        """Generate recommendations based on detected anomalies."""
        recommendations = []

        if 'perfect_r2' in anomaly_types or 'impossible_combination' in anomaly_types:
            recommendations.extend([
                "🚨 CRITICAL: Data leakage detected!",
                "• Immediately review data preprocessing pipeline",
                "• Check for target information in features",
                "• Verify temporal ordering of train/test splits",
                "• Consider restarting with clean data splits"
            ])

        if 'suspicious_r2' in anomaly_types:
            recommendations.extend([
                "⚠️ Suspicious performance detected",
                "• Review model complexity and regularization",
                "• Check for overfitting to specific patterns",
                "• Validate results with independent test set"
            ])

        if 'perfect_mae' in anomaly_types or 'perfect_rmse' in anomaly_types:
            recommendations.extend([
                "⚠️ Extremely low error metrics detected",
                "• Verify metric calculation correctness",
                "• Check for scale issues in data",
                "• Consider cross-validation for robustness"
            ])

        if not recommendations:
            recommendations.append("✅ No critical anomalies detected")

        return recommendations

    def print_anomaly_report(self):
        """Print comprehensive anomaly detection report."""
        summary = self.get_anomaly_summary()

        print("\n" + "="*60)
        print("🔍 MODEL PERFORMANCE ANOMALY REPORT")
        print("="*60)

        print(f"📊 Summary:")
        print(f"   • Total models monitored: {summary['total_models_monitored']}")
        print(f"   • Models with anomalies: {summary['models_with_anomalies']}")
        print(f"   • Anomaly rate: {summary['anomaly_rate']:.1%}")
        print(f"   • Total anomalies detected: {summary['total_anomalies']}")

        if summary['anomaly_types']:
            print(f"\n🚨 Anomaly Types:")
            for anomaly_type, anomalies in summary['anomaly_types'].items():
                print(f"   • {anomaly_type}: {len(anomalies)} occurrences")

        print(f"\n📋 Recommendations:")
        for rec in summary['recommendations']:
            print(f"   {rec}")

        print("="*60)


# Global model performance monitor instance
_global_model_monitor = None

def get_model_performance_monitor() -> ModelPerformanceMonitor:
    """Get the global model performance monitor instance."""
    global _global_model_monitor
    if _global_model_monitor is None:
        _global_model_monitor = ModelPerformanceMonitor()
    return _global_model_monitor
