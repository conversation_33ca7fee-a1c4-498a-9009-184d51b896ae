#!/usr/bin/env python3
"""
Comprehensive Data Quality Diagnostics Module

This module provides comprehensive data quality analysis before sequence creation
to identify and diagnose issues that prevent successful sequence generation.
It helps users understand why sequences can't be created and provides actionable feedback.

Author: SAITS Model Optimization Team
Date: 2024
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class WellDataQuality:
    """Data quality metrics for a single well."""
    well_name: str
    total_rows: int
    valid_rows: int
    missing_rate: float
    continuous_intervals: List[Tuple[int, int]]  # (start, end) indices
    longest_interval: int
    feature_completeness: Dict[str, float]
    data_range_issues: List[str]
    outlier_count: int
    quality_score: float

@dataclass
class DatasetQualityReport:
    """Comprehensive dataset quality report."""
    total_wells: int
    total_rows: int
    overall_missing_rate: float
    well_qualities: List[WellDataQuality]
    sequence_feasibility: Dict[int, int]  # seq_length -> possible_sequences
    recommended_sequence_length: int
    critical_issues: List[str]
    warnings: List[str]
    actionable_recommendations: List[str]
    can_create_sequences: bool

class DataQualityDiagnostics:
    """Comprehensive data quality diagnostics for well log data."""
    
    def __init__(self, feature_columns: List[str]):
        """
        Initialize data quality diagnostics.
        
        Args:
            feature_columns: List of feature column names to analyze
        """
        self.feature_columns = feature_columns
        self.well_log_ranges = {
            'GR': (0, 300),      # Gamma Ray (API units)
            'NPHI': (-0.15, 1.0), # Neutron Porosity (fraction)
            'RHOB': (1.0, 3.5),   # Bulk Density (g/cm³)
            'RT': (0.1, 10000),   # Resistivity (ohm-m)
            'P-WAVE': (1500, 7000) # P-wave velocity (m/s)
        }
    
    def analyze_well_data_quality(self, df: pd.DataFrame, well_column: str = 'WELL') -> DatasetQualityReport:
        """
        Perform comprehensive data quality analysis on well log dataset.
        
        Args:
            df: DataFrame containing well log data
            well_column: Name of the column containing well identifiers
            
        Returns:
            DatasetQualityReport with comprehensive analysis
        """
        logger.info("🔍 Starting comprehensive data quality analysis...")
        
        # Initialize report components
        well_qualities = []
        total_rows = len(df)
        critical_issues = []
        warnings = []
        recommendations = []
        
        # Analyze each well individually
        wells = df[well_column].unique() if well_column in df.columns else ['Unknown']
        
        for well_name in wells:
            if well_column in df.columns:
                well_data = df[df[well_column] == well_name]
            else:
                well_data = df
                well_name = 'Unknown'
            
            well_quality = self._analyze_single_well(well_data, well_name)
            well_qualities.append(well_quality)
        
        # Calculate overall statistics
        total_wells = len(wells)
        overall_missing_rate = self._calculate_overall_missing_rate(df)
        
        # Analyze sequence feasibility
        sequence_feasibility = self._analyze_sequence_feasibility(well_qualities)
        recommended_length = self._recommend_sequence_length(sequence_feasibility)
        
        # Generate issues and recommendations
        critical_issues, warnings, recommendations = self._generate_issues_and_recommendations(
            well_qualities, sequence_feasibility, recommended_length
        )
        
        # Determine if sequences can be created
        can_create_sequences = any(count > 0 for count in sequence_feasibility.values())
        
        report = DatasetQualityReport(
            total_wells=total_wells,
            total_rows=total_rows,
            overall_missing_rate=overall_missing_rate,
            well_qualities=well_qualities,
            sequence_feasibility=sequence_feasibility,
            recommended_sequence_length=recommended_length,
            critical_issues=critical_issues,
            warnings=warnings,
            actionable_recommendations=recommendations,
            can_create_sequences=can_create_sequences
        )
        
        self._print_quality_report(report)
        return report
    
    def _analyze_single_well(self, well_data: pd.DataFrame, well_name: str) -> WellDataQuality:
        """Analyze data quality for a single well."""
        
        total_rows = len(well_data)
        if total_rows == 0:
            return WellDataQuality(
                well_name=well_name,
                total_rows=0,
                valid_rows=0,
                missing_rate=1.0,
                continuous_intervals=[],
                longest_interval=0,
                feature_completeness={},
                data_range_issues=[],
                outlier_count=0,
                quality_score=0.0
            )
        
        # Extract feature data
        feature_data = well_data[self.feature_columns].values
        
        # Calculate missing data statistics
        valid_mask = ~np.isnan(feature_data).any(axis=1)
        valid_rows = np.sum(valid_mask)
        missing_rate = 1.0 - (valid_rows / total_rows)
        
        # Find continuous intervals
        continuous_intervals = self._find_continuous_intervals(valid_mask)
        longest_interval = max([end - start for start, end in continuous_intervals], default=0)
        
        # Calculate feature completeness
        feature_completeness = {}
        for i, feature in enumerate(self.feature_columns):
            feature_valid = np.sum(~np.isnan(feature_data[:, i]))
            feature_completeness[feature] = feature_valid / total_rows if total_rows > 0 else 0.0
        
        # Check data range issues
        data_range_issues = self._check_data_ranges(well_data)
        
        # Count outliers
        outlier_count = self._count_outliers(feature_data)
        
        # Calculate overall quality score
        quality_score = self._calculate_quality_score(
            missing_rate, longest_interval, len(continuous_intervals), outlier_count, total_rows
        )
        
        return WellDataQuality(
            well_name=well_name,
            total_rows=total_rows,
            valid_rows=valid_rows,
            missing_rate=missing_rate,
            continuous_intervals=continuous_intervals,
            longest_interval=longest_interval,
            feature_completeness=feature_completeness,
            data_range_issues=data_range_issues,
            outlier_count=outlier_count,
            quality_score=quality_score
        )
    
    def _find_continuous_intervals(self, valid_mask: np.ndarray) -> List[Tuple[int, int]]:
        """Find continuous intervals of valid data."""
        
        if len(valid_mask) == 0 or not np.any(valid_mask):
            return []
        
        intervals = []
        start = None
        
        for i, is_valid in enumerate(valid_mask):
            if is_valid and start is None:
                start = i
            elif not is_valid and start is not None:
                intervals.append((start, i))
                start = None
        
        # Handle case where valid data extends to the end
        if start is not None:
            intervals.append((start, len(valid_mask)))
        
        return intervals
    
    def _check_data_ranges(self, well_data: pd.DataFrame) -> List[str]:
        """Check for data values outside expected ranges."""
        
        issues = []
        
        for feature in self.feature_columns:
            if feature not in well_data.columns:
                continue
                
            data = well_data[feature].dropna()
            if len(data) == 0:
                continue
            
            # Check against known ranges
            feature_upper = feature.upper()
            if feature_upper in self.well_log_ranges:
                min_expected, max_expected = self.well_log_ranges[feature_upper]
                
                below_min = np.sum(data < min_expected)
                above_max = np.sum(data > max_expected)
                
                if below_min > 0:
                    issues.append(f"{feature}: {below_min} values below expected minimum ({min_expected})")
                if above_max > 0:
                    issues.append(f"{feature}: {above_max} values above expected maximum ({max_expected})")
        
        return issues
    
    def _count_outliers(self, feature_data: np.ndarray) -> int:
        """Count outliers using IQR method."""
        
        outlier_count = 0
        
        for col in range(feature_data.shape[1]):
            col_data = feature_data[:, col]
            valid_data = col_data[~np.isnan(col_data)]
            
            if len(valid_data) < 4:  # Need at least 4 points for quartiles
                continue
            
            q1, q3 = np.percentile(valid_data, [25, 75])
            iqr = q3 - q1
            
            if iqr > 0:
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                outliers = np.sum((valid_data < lower_bound) | (valid_data > upper_bound))
                outlier_count += outliers
        
        return outlier_count

    def _calculate_overall_missing_rate(self, df: pd.DataFrame) -> float:
        """Calculate overall missing rate across all features."""
        feature_data = df[self.feature_columns]
        total_values = feature_data.size
        missing_values = feature_data.isnull().sum().sum()
        return missing_values / total_values if total_values > 0 else 1.0

    def _analyze_sequence_feasibility(self, well_qualities: List[WellDataQuality]) -> Dict[int, int]:
        """Analyze how many sequences can be created for different sequence lengths."""

        sequence_lengths = [64, 32, 16, 12, 8, 6, 4]
        feasibility = {}

        for seq_len in sequence_lengths:
            total_sequences = 0

            for well_quality in well_qualities:
                for start, end in well_quality.continuous_intervals:
                    interval_length = end - start
                    if interval_length >= seq_len:
                        # Assuming stride of 1
                        possible_sequences = interval_length - seq_len + 1
                        total_sequences += possible_sequences

            feasibility[seq_len] = total_sequences

        return feasibility

    def _recommend_sequence_length(self, feasibility: Dict[int, int]) -> int:
        """Recommend optimal sequence length based on data characteristics."""
        # Find the longest sequence length that can produce at least 10 sequences
        min_sequences_required = 10

        for seq_len in sorted(feasibility.keys(), reverse=True):
            if feasibility[seq_len] >= min_sequences_required:
                return seq_len

        # If no length can produce 10 sequences, find the length that produces the most
        if feasibility:
            best_length = max(feasibility.keys(), key=lambda x: feasibility[x])
            if feasibility[best_length] > 0:
                return best_length

        return 0  # No sequences can be created

    def _calculate_quality_score(self, missing_rate: float, longest_interval: int,
                               num_intervals: int, outlier_count: int, total_rows: int) -> float:
        """Calculate overall quality score for a well (0-1)."""
        # Base score from completeness
        completeness_score = 1.0 - missing_rate

        # Interval length score (normalized by total rows)
        interval_score = min(1.0, longest_interval / max(1, total_rows))

        # Outlier penalty
        outlier_penalty = min(0.5, outlier_count / max(1, total_rows))

        # Combined score
        quality_score = (completeness_score * 0.5 + interval_score * 0.4) * (1.0 - outlier_penalty)

        return max(0.0, min(1.0, quality_score))

    def _generate_issues_and_recommendations(self, well_qualities: List[WellDataQuality],
                                           feasibility: Dict[int, int], recommended_length: int) -> Tuple[List[str], List[str], List[str]]:
        """Generate critical issues, warnings, and actionable recommendations."""
        critical_issues = []
        warnings = []
        recommendations = []

        # Calculate overall missing rate
        overall_missing_rate = np.mean([wq.missing_rate for wq in well_qualities]) if well_qualities else 1.0

        # Critical issues
        if recommended_length == 0:
            critical_issues.append("No valid sequences can be created with any reasonable sequence length")
            critical_issues.append(f"Overall missing rate is {overall_missing_rate:.1%}")

        total_intervals = sum(len(wq.continuous_intervals) for wq in well_qualities)
        if total_intervals == 0:
            critical_issues.append("No continuous intervals found across all wells")

        # Warnings
        if overall_missing_rate > 0.5:
            warnings.append(f"High overall missing rate: {overall_missing_rate:.1%}")

        short_intervals = [wq for wq in well_qualities if wq.longest_interval < 32]
        if len(short_intervals) > len(well_qualities) * 0.5:
            warnings.append(f"{len(short_intervals)} wells have longest intervals < 32 rows")

        # Recommendations
        if recommended_length > 0:
            recommendations.append(f"Use sequence length of {recommended_length} (can create {feasibility[recommended_length]} sequences)")
        else:
            # Find the best possible option
            max_sequences = max(feasibility.values()) if feasibility else 0
            if max_sequences > 0:
                best_length = max(feasibility.keys(), key=lambda x: feasibility[x])
                recommendations.append(f"Consider sequence length of {best_length} (can create {max_sequences} sequences)")

            recommendations.append("Consider data preprocessing to fill missing values")
            recommendations.append("Consider combining data from multiple wells")
            recommendations.append("Consider using shorter sequence lengths or different modeling approaches")

        # Data quality recommendations
        low_quality_wells = [wq for wq in well_qualities if wq.quality_score < 0.5]
        if low_quality_wells:
            recommendations.append(f"Improve data quality for {len(low_quality_wells)} wells with quality score < 0.5")

        return critical_issues, warnings, recommendations

    def _print_quality_report(self, report: DatasetQualityReport) -> None:
        """Print comprehensive quality report."""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE DATA QUALITY DIAGNOSTICS REPORT")
        print("="*80)

        # Dataset overview
        print(f"\n📈 DATASET OVERVIEW:")
        print(f"   • Total Wells: {report.total_wells}")
        print(f"   • Total Rows: {report.total_rows:,}")
        print(f"   • Overall Missing Rate: {report.overall_missing_rate:.1%}")
        print(f"   • Can Create Sequences: {'✅ YES' if report.can_create_sequences else '❌ NO'}")

        # Sequence feasibility
        print(f"\n🎯 SEQUENCE FEASIBILITY ANALYSIS:")
        print(f"   • Recommended Sequence Length: {report.recommended_sequence_length}")

        print(f"\n   Sequence Length → Possible Sequences:")
        for seq_len in sorted(report.sequence_feasibility.keys()):
            count = report.sequence_feasibility[seq_len]
            status = "✅" if count > 0 else "❌"
            print(f"     {status} Length {seq_len:3d}: {count:4d} sequences")

        # Well-by-well analysis
        print(f"\n🏭 WELL-BY-WELL ANALYSIS:")
        for wq in report.well_qualities:
            quality_emoji = "🟢" if wq.quality_score > 0.7 else "🟡" if wq.quality_score > 0.4 else "🔴"
            print(f"   {quality_emoji} {wq.well_name}:")
            print(f"      • Rows: {wq.total_rows:,} (Valid: {wq.valid_rows:,})")
            print(f"      • Missing Rate: {wq.missing_rate:.1%}")
            print(f"      • Longest Interval: {wq.longest_interval} rows")
            print(f"      • Quality Score: {wq.quality_score:.3f}")
            print(f"      • Continuous Intervals: {len(wq.continuous_intervals)}")

            if wq.data_range_issues:
                print(f"      • Data Issues: {len(wq.data_range_issues)}")
                for issue in wq.data_range_issues[:2]:  # Show first 2 issues
                    print(f"        - {issue}")

        # Critical issues
        if report.critical_issues:
            print(f"\n❌ CRITICAL ISSUES:")
            for issue in report.critical_issues:
                print(f"   • {issue}")

        # Warnings
        if report.warnings:
            print(f"\n⚠️  WARNINGS:")
            for warning in report.warnings:
                print(f"   • {warning}")

        # Recommendations
        if report.actionable_recommendations:
            print(f"\n💡 ACTIONABLE RECOMMENDATIONS:")
            for i, rec in enumerate(report.actionable_recommendations, 1):
                print(f"   {i}. {rec}")

        print("\n" + "="*80)


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    feature_columns = ['GR', 'NPHI', 'RHOB', 'RT', 'P-WAVE']
    diagnostics = DataQualityDiagnostics(feature_columns)

    # Create sample data for testing
    np.random.seed(42)
    sample_data = {
        'WELL': ['Well_A'] * 50 + ['Well_B'] * 30 + ['Well_C'] * 20,
        'GR': np.random.normal(75, 25, 100),
        'NPHI': np.random.normal(0.2, 0.1, 100),
        'RHOB': np.random.normal(2.3, 0.3, 100),
        'RT': np.random.lognormal(2, 1, 100),
        'P-WAVE': np.random.normal(4000, 500, 100)
    }

    # Introduce some missing values
    df_sample = pd.DataFrame(sample_data)
    missing_indices = np.random.choice(100, 30, replace=False)
    df_sample.loc[missing_indices, 'GR'] = np.nan

    # Run diagnostics
    report = diagnostics.analyze_well_data_quality(df_sample)

    print(f"\n🎯 EXAMPLE ANALYSIS COMPLETE:")
    print(f"   • Recommended sequence length: {report.recommended_sequence_length}")
    print(f"   • Can create sequences: {report.can_create_sequences}")
