"""
Test script to verify the tkinter error fix for cross-validation.

This script tests the matplotlib backend configuration and cross-validation
error handling to ensure the "main thread is not in main loop" error is resolved.
"""

import os
import sys
import warnings
import matplotlib
import numpy as np
import pandas as pd
from sklearn.neighbors import KNeighborsRegressor
from sklearn.model_selection import cross_val_score

def test_matplotlib_backend_fix():
    """Test that matplotlib backend switching works correctly."""
    print("🧪 Testing matplotlib backend configuration...")
    
    # Test 1: Check initial backend
    initial_backend = matplotlib.get_backend()
    print(f"   Initial backend: {initial_backend}")
    
    # Test 2: Switch to Agg backend
    matplotlib.use('Agg', force=True)
    current_backend = matplotlib.get_backend()
    print(f"   After switching to Agg: {current_backend}")
    
    # Test 3: Verify no GUI components are created
    try:
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots(1, 1, figsize=(6, 4))
        ax.plot([1, 2, 3], [1, 4, 2])
        plt.close(fig)
        print("   ✅ Matplotlib operations work with Agg backend")
    except Exception as e:
        print(f"   ❌ Matplotlib test failed: {e}")
        return False
    
    return True

def test_cross_validation_safety():
    """Test cross-validation with KNN to reproduce and verify the fix."""
    print("\n🧪 Testing cross-validation safety with KNN...")
    
    # Create sample data similar to well log data
    np.random.seed(42)
    n_samples = 100
    n_features = 5
    
    # Simulate well log features (GR, NPHI, RHOB, DT, RT)
    X = pd.DataFrame({
        'GR': np.random.normal(50, 20, n_samples),
        'NPHI': np.random.normal(0.15, 0.05, n_samples),
        'RHOB': np.random.normal(2.3, 0.2, n_samples),
        'DT': np.random.normal(100, 30, n_samples),
        'RT': np.random.lognormal(1, 1, n_samples)
    })
    
    # Simulate P-WAVE target (similar to the error case)
    y = pd.Series(
        X['DT'] * 0.8 + X['RHOB'] * 20 + np.random.normal(0, 5, n_samples),
        name='P_WAVE'
    )
    
    print(f"   Created test dataset: {X.shape[0]} samples, {X.shape[1]} features")
    
    # Test 3: Cross-validation with KNN (the model that caused the error)
    try:
        from utils.crossvalidation_utils import safe_cross_validation_wrapper, WellLogCrossValidator
        
        # Create KNN model
        knn_model = KNeighborsRegressor(n_neighbors=15, weights='distance')
        
        # Test with the safe wrapper
        cv_validator = WellLogCrossValidator(cv_strategy='timeseries', n_splits=5)
        
        print("   Running cross-validation with safety wrapper...")
        cv_results = safe_cross_validation_wrapper(cv_validator.validate_model, knn_model, X, y)
        
        print(f"   ✅ Cross-validation completed successfully!")
        print(f"      MAE: {cv_results['mae_mean']:.3f} ± {cv_results['mae_std']:.3f}")
        print(f"      R²:  {cv_results['r2_mean']:.3f} ± {cv_results['r2_std']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cross-validation test failed: {e}")
        return False

def test_environment_configuration():
    """Test that environment variables are properly configured."""
    print("\n🧪 Testing environment configuration...")
    
    # Check MPLBACKEND setting
    mpl_backend = os.environ.get('MPLBACKEND', 'Not set')
    print(f"   MPLBACKEND environment variable: {mpl_backend}")
    
    if mpl_backend == 'Agg':
        print("   ✅ MPLBACKEND correctly set to Agg")
        return True
    else:
        print("   ⚠️ MPLBACKEND not set to Agg - may cause GUI issues")
        return False

def run_comprehensive_test():
    """Run all tests to verify the tkinter fix."""
    print("🔧 TKINTER ERROR FIX VERIFICATION")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Matplotlib backend
    if test_matplotlib_backend_fix():
        tests_passed += 1
    
    # Test 2: Environment configuration
    if test_environment_configuration():
        tests_passed += 1
    
    # Test 3: Cross-validation safety
    if test_cross_validation_safety():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - Tkinter error fix is working correctly!")
        print("\n💡 The fix includes:")
        print("   • Automatic matplotlib backend switching to Agg")
        print("   • GUI warning suppression during cross-validation")
        print("   • Safe wrapper for cross-validation functions")
        print("   • Environment variable configuration")
        return True
    else:
        print("❌ SOME TESTS FAILED - Please check the configuration")
        return False

if __name__ == "__main__":
    # Configure environment before running tests
    os.environ['MPLBACKEND'] = 'Agg'
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings('ignore')
    
    # Run the comprehensive test
    success = run_comprehensive_test()
    
    if success:
        print("\n🎉 You can now run cross-validation without tkinter errors!")
    else:
        print("\n⚠️ Please check the error messages above and ensure all fixes are properly applied.")
