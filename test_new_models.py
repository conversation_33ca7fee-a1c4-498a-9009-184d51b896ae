#!/usr/bin/env python3
"""
Test script for newly added ML models: KNN, Decision Tree, Extra Trees, Random Forest
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from preprocessing.ml_core import MODEL_REGISTRY, create_enhanced_model_instance
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score

def test_new_models():
    """Test the four newly added models"""
    
    # Test models
    new_models = ['knn', 'decision_tree', 'extra_trees', 'random_forest']
    
    print("[TEST] Testing newly added ML models...")
    
    # Create synthetic test data
    np.random.seed(42)
    n_samples = 1000
    n_features = 5
    
    X = np.random.randn(n_samples, n_features)
    y = X[:, 0] * 2 + X[:, 1] * 1.5 + np.random.randn(n_samples) * 0.1
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    results = {}
    
    for model_key in new_models:
        try:
            print(f"\n[MODEL] Testing {model_key}...")
            
            # Get model configuration
            model_config = MODEL_REGISTRY[model_key]
            
            # Create model with default hyperparameters
            hyperparams = {}
            for param, config in model_config['hyperparameters'].items():
                hyperparams[param] = config['default']
            
            # Create model instance
            model = create_enhanced_model_instance(model_config, hyperparams)
            
            # Train model
            model.fit(X_train, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test)
            
            # Calculate metrics
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            results[model_key] = {
                'mae': mae,
                'r2': r2,
                'status': 'SUCCESS'
            }
            
            print(f"   [SUCCESS] MAE: {mae:.4f}, R²: {r2:.4f}")
            
        except Exception as e:
            results[model_key] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"   [ERROR] Failed: {e}")
    
    # Summary
    print("\n[SUMMARY] Test Summary:")
    successful = sum(1 for r in results.values() if r['status'] == 'SUCCESS')
    print(f"   [SUCCESS] Successful: {successful}/{len(new_models)}")
    
    if successful == len(new_models):
        print("   [SUCCESS] All new models integrated successfully!")
    else:
        print("   [WARNING] Some models failed - check implementation")
    
    return results

if __name__ == "__main__":
    test_new_models()