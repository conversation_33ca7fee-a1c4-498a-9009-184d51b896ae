# Cleanup Plan: Resize to Minimum Model Set (Remove SAITS, BRITS, Transformer, and mRNN)

Goal: Remove SAITS, BRITS (PyPOTS-based), Transformer, and mRNN models while keeping the ML Log Prediction pipeline fully functional with:
- Gradient boosting models: XGBoost, LightGBM, CatBoost
- Basic neural network model (models/neuralnet.py)
- Minimal preprocessing foundation for future BiGRU/Transformer models
- Core reporting, visualization, and GPU/memory optimizations

This document provides an end-to-end plan with exact file paths, approximate line references, and a safe execution order.

---

## Recommended Execution Order (High-Level)
1. Create a feature branch (e.g., feat/minimal-models-cleanup)
2. Update model registry logic (preprocessing/ml_core.py)
3. Remove advanced-model files (models/advanced_models/*) and deep learning diagnostics
4. Update imports, conditionals, and references across the codebase
5. Clean dependencies (remove PyPOTS) and optional deep learning extras
6. Update configuration text and documentation
7. Run smoke checks and tests
8. Iterate on any residual references

---

## Important: Preprocessing Strategy for Future BiGRU/Transformer Support

### Essential Preprocessing to KEEP (for future deep learning):
- **utils/data_leakage_detector.py** - Critical for any ML pipeline
  - Prevents overfitting and data leakage
  - Essential for both current and future models
  
- **utils/data_quality_diagnostics.py** - Essential for data validation
  - Identifies data issues before modeling
  - Important for understanding sequence quality
  
- **preprocessing/data_handler.py** - Core data handling
  - `clean_log_data()` - Basic range-based cleaning
  - `normalize_data()` - Essential for neural networks (keep for future BiGRU/Transformer)
  - `create_sequences()` - Basic sequence creation (keep simplified version)
  
- **utils/performance_monitor.py** - Performance monitoring
  - Detects suspicious metrics

### Preprocessing to SIMPLIFY (not remove entirely):
- **Basic sequence creation in data_handler.py**
  - Keep the core `create_sequences()` function but remove enhanced features
  - This provides foundation for future BiGRU/Transformer without current bloat
  
- **Data normalization in data_handler.py**
  - Keep `normalize_data()` as neural networks require normalized inputs
  - Remove the "enhanced" version references

### Redundant/Bloated Preprocessing to REMOVE:
- **Property-aware interpolation** (from enhanced_preprocessing.py)
  - Over-engineered even for deep learning
  - Future models can use simple interpolation or handle missing data directly
  
- **Geological constraint validation** (from enhanced_preprocessing.py)
  - `clean_log_data()` already provides sufficient validation
  - No need for duplicate systems
  
- **utils/interval_analyzer.py** - Entire file
  - SAITS/BRITS specific implementation
  - Future models can use simpler sequence validation
  
- **preprocessing/data_clipping.py** - Entire file
  - Model-specific implementation
  - Future models should have their own preprocessing modules
  
- **Complex sequence optimization**
  - Remove adaptive_sequence_optimizer.py, adaptive_sequence_creator.py
  - Keep sequence creation simple and extend when needed
  
- **Most of preprocessing/deep/** directory
  - Remove entire directory except what's moved to main preprocessing

### Architecture Principle:
Keep a clean, minimal foundation that can be extended when BiGRU/Transformer are added, rather than preserving complex features "just in case". Future models can add their specific preprocessing needs in a modular way.

---

## 1) Model Registry Cleanup (preprocessing/ml_core.py)
File: preprocessing/ml_core.py (total ~2,761 lines)

- Remove/disable advanced model imports and usage:
  - Around line ~75: import of SAITS/BRITS and ADVANCED_* from models.advanced_models
    - Current: `from models.advanced_models import (SAITSModel, BRITSModel, ADVANCED_MODELS_AVAILABLE, ADVANCED_MODELS_STATUS, ...)`
    - Action: Remove this import entirely OR keep the try/except but point to no-ops
  - Around lines ~90–95: Fallback assignments when ImportError occurs
    - These are okay to keep; if advanced_models is removed, fallback engages automatically

- Remove SAITS/BRITS entries from enhanced registry config block:
  - Around lines ~236–308: Advanced model configuration and registry update
    - Remove/disable the SAITS block (~241–275)
    - Remove/disable the BRITS block (~276–308)
    - Keep the surrounding logic intact or remove the entire ADVANCED_MODEL_CONFIGS section

- Remove SAITS/BRITS/Transformer/mRNN from recommendation helpers:
  - Around line ~465: `recommendations.extend(['saits', 'brits'])` → remove both
  - Around line ~474: `recommendations.extend(['brits'])` → remove
  - Around lines ~611–615: conditional append of saits/brits → remove
  - Around lines ~624–626: conditional append of brits → remove
  - Note: Lines ~313 and ~317 show Transformer and mRNN have already been removed from registry

- Remove lingering mentions in docstrings/strings:
  - Around ~1078, ~1151, ~1518: docstrings describing SAITS/BRITS/Transformer → update to refer to gradient boosting models
  - Around ~1437, ~1802: user guidance strings that recommend SAITS/BRITS → remove or reword for shallow models
  - Around ~2324–2336: stability_model_type mapping for 'saits'/'brits'/'transformer' → remove branches
  - Around ~2335: comment "Detect PyPOTS-based models" → remove/neutralize
  - Around ~641, ~718-719, ~953, ~956, ~971, ~1070, ~1072, ~1098: Transformer-related comments and logic
  - Around ~1862: mRNN detection logic

- Remove transformer/mRNN specific functions:
  - Around ~953-971: `evaluate_prediction_only_transformer` function → remove entirely
  - Around ~641: Enhanced model configuration for transformer modes → simplify or remove
  - Around ~718-719: Transformer-specific configuration logic → remove

- **KEEP sequence creation helpers for future use**

Rationale: Ensures MODEL_REGISTRY and helper logic won't expose or suggest any deep learning models except basic neural network.

---

## 2) File Removal Strategy
Safely delete the following (ImportError paths are already handled gracefully in code):

- Advanced model implementations and utils (entire directory is safe to remove):
  - models/advanced_models/
    - models/advanced_models/saits_model.py
    - models/advanced_models/brits_model.py
    - models/advanced_models/base_model.py
    - models/advanced_models/utils/
      - models/advanced_models/utils/data_preparation.py
    - models/advanced_models/__init__.py

- Deep learning specific diagnostics and over-engineered preprocessing (REMOVE):
  - preprocessing/deep/saits_error_diagnostics_and_fixes.py
  - preprocessing/deep/enhanced_preprocessing.py (property-aware interpolation)
  - preprocessing/deep/adaptive_sequence_optimizer.py (over-engineered)
  - preprocessing/deep/adaptive_sequence_creator.py (over-engineered)
  - preprocessing/data_clipping.py (model-specific)
  - preprocessing/adaptive_sequence_creator.py (if exists at root level)
  - utils/diagnostic_coordinator.py (SAITS-specific)
  - utils/prediction_data_processor.py (Transformer-specific)
  - utils/interval_analyzer.py (SAITS-specific interval analysis)
  - utils/sequence_optimization.py (over-engineered)
  - utils/parameter_standardization.py (SAITS-specific)

- Archives: tests and docs specifically about SAITS/BRITS/Transformer/mRNN (optional cleanup; does not affect runtime)
  - archives/functions/diagnose_saits_issue.py
  - archives/functions/saits_error_diagnostics_and_fixes.py
  - archives/functions/test_saits_pipeline_fix.py
  - Any other SAITS/BRITS/Transformer/mRNN-only scripts in archives/functions/
  - Deep learning markdowns (documentation):
    - archives/markdowns/SAITS_DIAGNOSTICS_INTEGRATION.md
    - archives/markdowns/SAITS_PIPELINE_FIX_SUMMARY.md
    - archives/markdowns/7_saits_integration_solution.md
    - archives/markdowns/6_saits_integration_problem_opt3 (folder)
    - archives/markdowns/b_saits_problem.md
    - archives/markdowns/2_optimize_saits_and_brits.md
  - Tests referenced in README but potentially absent now:
    - archives/test_files/test_full_saits.py (if exists)

Notes:
- After deletion, preprocessing/ml_core.py's try/except around advanced_models will fall back to basic models.
- Keep models/neuralnet.py as the basic neural network option.
- The entire preprocessing/deep/ directory can be removed

---

## 3) Code References Audit (imports, calls, conditionals)

### preprocessing/ml_core.py
- Import and ADVANCED_* usage: ~75, ~90–95
- Advanced registry blocks: ~241–308 (SAITS, BRITS definitions)
- Recommendation helpers: ~465, ~474, ~611–615, ~624–626
- Docstrings/messages mentioning SAITS/BRITS/Transformer: ~1078, ~1151, ~1518, ~1437, ~1802
- Stability/model-type branches: ~2324–2336; PyPOTS model detection: ~2335
- Transformer-specific code: ~641, ~718-719, ~953-971, ~1070, ~1072, ~1098
- mRNN detection: ~1862

### preprocessing/ml_core_phase1_integration.py
- Lines ~984–1023: SAITS model diagnostics integration → remove entire block
- Line ~1204: Comment about "Data Clipping for SAITS/BRITS models" → remove entire section
- Remove complex sequence optimization code
- **KEEP:** Basic validation and simple sequence creation logic

### preprocessing/data_handler.py
- **KEEP:** `create_sequences()` function but simplify it
- **KEEP:** `normalize_data()` function (needed for future neural networks)
- Remove `use_enhanced` parameters and enhanced preprocessing references
- Simplify sequence creation to basic sliding window approach

### main.py
- Lines ~112–115: "Step 6.5: Configure data clipping for SAITS/BRITS models" → remove entire step
- Lines ~148–150: Selection guide mentions SAITS and BRITS → remove these lines
- Lines ~155–160: Mentions Transformer and mRNN as versatile models → remove these lines
- Remove any references to enhanced preprocessing or data clipping

### config_handler.py
- Line ~153: "Choose the training pipeline for SAITS/BRITS models:" → remove entire function
- Lines ~341, ~394: deep_models lists include 'saits', 'brits', 'transformer', 'mrnn' → remove all four
- Lines ~444–457: SAITS/BRITS hyperparameter defaults → remove both blocks
- Lines ~472–504: configure_data_clipping function → remove entire function
- Line ~585: Loop over ['saits', 'brits'] → remove

### utils/ directory files:
- **KEEP THESE FILES (essential for any ML pipeline):**
  - utils/data_leakage_detector.py - No changes needed
  - utils/data_quality_diagnostics.py - No changes needed
  - utils/performance_monitor.py - No changes needed
  
- **DELETE THESE FILES:**
  - utils/interval_analyzer.py - SAITS-specific interval analysis
  - utils/sequence_optimization.py - Over-engineered
  - utils/parameter_standardization.py - SAITS-specific
  - utils/diagnostic_coordinator.py - SAITS-specific
  - utils/prediction_data_processor.py - Transformer-specific
  
- **MODIFY THESE FILES:**
  - utils/training_optimization.py:
    - Lines ~2: Module docstring mentions mRNN → update
    - Lines ~290, ~299: Remove 'transformer' batch size mappings
    - Lines ~291-292, ~300-301: Remove SAITS/BRITS batch size mappings
    - **KEEP:** Basic batch size optimization logic for future models
  - utils/stability_core.py:
    - **KEEP:** Basic validation functions
    - Remove deep model specific validation
    - Lines ~852, ~855: Remove 'transformer' and 'mrnn' stability factors
    - Lines ~853-854: Remove SAITS/BRITS stability factors
    - Lines ~937-938: Remove Transformer scheduler logic
    - Lines ~947-948: Remove SAITS/BRITS scheduler logic
  - utils/hyperparameter_tuning.py:
    - Lines ~67-79: Remove all deep model parameter suggestion logic
    - Lines ~85-98: Remove `_suggest_transformer_params` method
    - Lines ~99-109: Remove `_suggest_mrnn_params` method
    - Lines ~119-132: Remove SAITS/BRITS parameter methods
    - **KEEP:** Framework for future hyperparameter tuning
  - utils/display_utils.py:
    - Lines ~502-504: Remove SAITS/BRITS/Transformer from example rankings
  - utils/visualization_advanced.py:
    - Line ~118: Remove transformer prediction-only check
  - utils/output_formatter.py:
    - Lines ~19, ~83, ~88, ~348: Remove prediction-only transformer references
  - utils/memory_optimization.py:
    - Line ~359: Update comment about mRNN optimization
    - **KEEP:** Memory optimization framework for future deep learning

### README.md (documentation updates listed in section 7)

### CLAUDE.md
- Deep Learning Models section mentions SAITS/BRITS/Transformer/mRNN → update to only mention basic neural network
- Add note about extensibility for future BiGRU/Transformer models
- File structure sample shows advanced_models/ → update

---

## 4) Dependency Cleanup
File: requirements.txt

- Remove PyPOTS (used only by SAITS/BRITS):
  - Line ~23: `pypots>=0.5.0  # SAITS, BRITS advanced imputation models` → delete

- **KEEP these for future deep learning:**
  - PyTorch and related packages (needed for BiGRU/Transformer)
  - scikit-learn, pandas, numpy, scipy (core dependencies)
  
- Optional removals (if truly unused):
  - MONAI (line ~26): currently not referenced → may remove
  - torchvision (line ~22): only if not planning vision models → may remove

After editing requirements.txt:
- Recreate your env or run: `pip uninstall pypots` (and `monai` if removed)

---

## 5) Configuration and UI Text Updates

### main.py
- Remove entire Step 6.5 (lines ~112–115): Data clipping configuration
- Remove lines ~148–160: Well Log Model Selection Guide that mentions deep models
- Simplify model selection to focus on gradient boosting models + basic neural network
- **Keep:** Basic workflow that can accommodate future models

### config_handler.py
- Remove `configure_data_clipping` function entirely
- Remove `configure_deep_learning_pipeline` function
- Remove all current deep model specific configurations
- **Keep:** Hyperparameter configuration framework for extensibility

### Simplified workflow:
- Use only `clean_log_data()` for data cleaning
- Run data quality diagnostics
- Check for data leakage
- Train gradient boosting models
- (Future: easily add BiGRU/Transformer configuration)

---

## 6) Testing Strategy (post-removal)

### Quick smoke tests
- Run the main GUI flow and execute with:
  - XGBoost (GPU): ensure device='cuda' works or falls back cleanly
  - LightGBM (GPU): verify device='gpu' configuration
  - CatBoost (GPU): verify task_type='GPU' works
  - Basic neural network: verify it still works

### Test preserved preprocessing:
- Verify `create_sequences()` still works (for future deep learning)
- Test `normalize_data()` function
- Run data quality diagnostics
- Test leakage detection
- Verify `clean_log_data()` works correctly

### Suggested manual runs
- python main.py
  - Select a small LAS input from ./Las/
  - Configure feature logs and a target log
  - **Run data quality checks**
  - **Run leakage detection**
  - Run batch with all gradient boosting models

### Future-proofing tests
- Verify sequence creation produces valid output
- Check that normalization works correctly
- Ensure the codebase is ready for model additions

---

## 7) Documentation Updates

### README.md
- Model Registry Architecture: update to show only gradient boosting + basic NN
- Deep Learning Models list: remove all mentions of SAITS/BRITS/Transformer/mRNN
- **Add section:** "Extensibility for Future Models"
  - Mention that the architecture supports adding BiGRU/Transformer
  - Note that basic sequence creation and normalization are preserved
- Remove references to complex preprocessing
- **Focus on:**
  - Clean, modular architecture
  - Data quality diagnostics
  - Leakage detection
  - Extensibility for future models
- Testing section: remove commands referencing deep learning tests
- Project Structure tree: remove advanced_models and preprocessing/deep directories

### CLAUDE.md
- Deep Learning Models: update to only mention basic neural network
- **Add note:** "Architecture designed for easy addition of BiGRU/Transformer models"
- File structure dependencies: remove advanced_models and deep preprocessing entries
- Update feature list to reflect simplified, extensible pipeline

### Clean up archives/markdowns
- Move all deep learning related docs to a "legacy" folder or delete

---

## 8) Rollback Plan
- If any regression occurs:
  - Revert the feature branch
  - Restore requirements.txt with pypots
  - Restore models/advanced_models and deep learning utilities

---

## Appendix: Quick Reference (Paths and Edits)

### Remove files/dirs
- models/advanced_models/ (entire directory)
- preprocessing/deep/ (entire directory)
- preprocessing/data_clipping.py
- preprocessing/adaptive_sequence_creator.py
- utils/diagnostic_coordinator.py
- utils/prediction_data_processor.py
- utils/interval_analyzer.py
- utils/sequence_optimization.py
- utils/parameter_standardization.py
- archives/functions/*saits*.*, archives/functions/*transformer*.*, archives/functions/*mrnn*.*
- archives/markdowns/*saits*.*, archives/markdowns/*transformer*.*, archives/markdowns/*mrnn*.*

### KEEP these files (foundation for future models)
- utils/data_leakage_detector.py
- utils/data_quality_diagnostics.py
- utils/performance_monitor.py
- preprocessing/data_handler.py (with create_sequences, normalize_data, clean_log_data)
- utils/memory_optimization.py (for future deep learning)
- utils/gpu_acceleration.py (for future deep learning)

### Edit files
- preprocessing/ml_core.py: 
  - Remove all current deep model references
  - Keep sequence-related helpers
- preprocessing/ml_core_phase1_integration.py: 
  - Remove complex features, keep basic validation
- preprocessing/data_handler.py:
  - Simplify create_sequences() but keep it
  - Keep normalize_data() for future neural networks
  - Remove enhanced preprocessing references
- main.py: 
  - Remove Step 6.5 and deep model references
  - Keep modular workflow
- config_handler.py: 
  - Remove current deep model configurations
  - Keep framework for future extensibility
- utils/training_optimization.py: keep basic optimization framework
- utils/stability_core.py: keep basic validation
- utils/hyperparameter_tuning.py: remove current deep models, keep framework
- requirements.txt: remove pypots, keep PyTorch for future
- README.md / CLAUDE.md: update to reflect extensible architecture

### Verification checklist
- [ ] Model registry only shows XGBoost, LightGBM, CatBoost, and basic neural network
- [ ] No import errors when running main.py
- [ ] All gradient boosting models work correctly
- [ ] Basic neural network works correctly
- [ ] create_sequences() function still works (for future models)
- [ ] normalize_data() function works correctly
- [ ] Data quality diagnostics work correctly
- [ ] Leakage detection functions properly
- [ ] clean_log_data() provides sufficient data cleaning
- [ ] No references to SAITS/BRITS/Transformer/mRNN in user-facing text
- [ ] Architecture ready for future BiGRU/Transformer addition
- [ ] Documentation reflects simplified, extensible pipeline

This plan creates a minimal, focused ML pipeline while preserving the essential foundation needed for future BiGRU and Transformer models. The key is maintaining clean, simple implementations of sequence creation and normalization without the current over-engineered complexity.